import React, { useState } from 'react'
import { Button, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, TextField, CircularProgress } from '@material-ui/core'


export const FormOfDrawnPolygonName = ({openForm, setOpenForm, polygonName, onSavePolygon, onCancelPolygon, savingPolygon}) => {
	const [nameOfPolygon, setNameOfPolygon] = useState("")

	const handleClose = () => {
		setOpenForm(false)
		setNameOfPolygon("") // Limpiar el campo al cerrar
		// Llamar a la función de cancelación si está disponible
		if (onCancelPolygon) {
			onCancelPolygon();
		}
	}

	const handleSaveName = async () => {
		if (!nameOfPolygon.trim()) {
			alert('Por favor ingresa un nombre para el polígono.');
			return;
		}

		// Llamar a la función de guardado pasada como prop
		if (onSavePolygon) {
			await onSavePolygon(nameOfPolygon.trim());
		} else {
			// Fallback al comportamiento anterior si no se pasa onSavePolygon
			polygonName(nameOfPolygon);
			handleClose();
		}
	}
  return (
	<div>
		<Dialog
			open={openForm}
			onClose={!savingPolygon ? handleClose : undefined}
			aria-labelledby="form-dialog-title"
			disableBackdropClick={savingPolygon}
			disableEscapeKeyDown={savingPolygon}
		>
        	<DialogTitle id="form-dialog-title">
				{savingPolygon ? 'Guardando Polígono...' : 'Nombre del Polígono'}
			</DialogTitle>
			<DialogContent>
				{savingPolygon ? (
					<div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '20px' }}>
						<CircularProgress size={24} style={{ marginRight: '10px' }} />
						<DialogContentText>
							Guardando el polígono en Firebase...
						</DialogContentText>
					</div>
				) : (
					<>
						<DialogContentText>
							Ingresa un nombre para el polígono que dibujaste. Este se guardará automáticamente en Firebase.
						</DialogContentText>
						<TextField
							autoFocus
							margin="dense"
							id="name"
							label="Nombre del polígono"
							value={nameOfPolygon}
							onChange={(event) => setNameOfPolygon(event.target.value)}
							fullWidth
							disabled={savingPolygon}
							onKeyDown={(e) => {
								if (e.key === 'Enter' && !savingPolygon) {
									handleSaveName();
								}
							}}
						/>
					</>
				)}
			</DialogContent>
			<DialogActions>
				{!savingPolygon && (
					<>
						<Button onClick={handleClose} color="default">
							Cancelar
						</Button>
						<Button
							variant='contained'
							onClick={handleSaveName}
							color="primary"
							disabled={!nameOfPolygon.trim()}
						>
							Guardar Polígono
						</Button>
					</>
				)}
			</DialogActions>
      </Dialog>
	</div>
  )
}
