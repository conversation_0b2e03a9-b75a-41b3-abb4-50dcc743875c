import React, { useState } from 'react'
import DigestFetch from 'digest-fetch';

export const CameraTesting = () => {
	const [imgSrc, setImgSrc] = useState(null);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState(null);

	const api_snapshot_request = process.env.REACT_APP_GET_SNAPSHOT_FROM_CAMERA
  const api_http_stream = process.env.REACT_APP_GET_HTTP_STREAM_FROM_CAMERA
	const user = process.env.REACT_APP_CAMERA_USER
	const pass = process.env.REACT_APP_CAMERA_PASS


	const handleFetchImage = async () => {
    setLoading(true);
    setError(null);
    try {
      
      // Creamos un cliente DigestFetch con usuario y contraseña
      const client = new DigestFetch(user, pass, { algorithm: 'MD5' /* opcional: 'SHA-256' */, qop: 'auth' });
      
      // Al <PERSON>amar a client.fetch, internamente hará el 401 inicial,
      // leerá el WWW-Authenticate y volverá a intentar con la cabecera correcta.
      const response = await client.fetch(api_snapshot_request);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status} – ${response.statusText}`);
      }
      const blob = await response.blob();
      setImgSrc(URL.createObjectURL(blob));
    } catch (err) {
      console.error(err);
      setError('No se pudo cargar la imagen: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
	<>
	<div style={{ textAlign: 'center', marginTop: '2rem' }}>
      <button onClick={handleFetchImage} disabled={loading}>
        {loading ? 'Cargando...' : 'Cargar imagen'}
      </button>

      {error && (
        <p style={{ color: 'red' }}>{error}</p>
      )}

      {imgSrc && (
        <div style={{ marginTop: '1rem' }}>
          <img 
            src={imgSrc} 
            alt="Desde la API" 
            style={{ maxWidth: '100%', height: 'auto', border: '1px solid #ccc' }}
          />
        </div>
      )}
    </div>

	<div style={{ textAlign: 'center' }}>
      <h3>Stream MJPEG</h3>
      <img
        src={api_http_stream}
        alt="Stream de la cámara"
        style={{ maxWidth: '100%' }}
      />
    </div>
	</>
  )
}
