import {
  Vpn<PERSON><PERSON>,
  FormatListNumbered,
  InsertInvitation,
  Power,
  Timeline,
  ImportContacts,
  AccountTree,
  Waves,
  Assistant,
  SettingsApplicationsRounded,
  Satellite,
  Videocam
} from "@material-ui/icons";

import PublishIcon from "@material-ui/icons/Publish";

import {
  DashboardWCtx,
  ConfigIO,
  Graph,
  Token,
  InfoUser,
  Manual,
  IOList,
  Tareas,
  TreeChart,
  Calendar,
  Timelapse,
  Crop,
  NewDataByCSV,
  IrrigationForm,
  ProfileSettings,
  SatelliteAnalysis,
} from "../components";
import GraphImproved from "../components/Views/Graph";
import { CameraTesting, WaterTankForm } from "../components/Views";
import WaterTankIcon from "../components/Common/Icons/WaterTankIcon";

const PATH_ADMIN = "/admin";
const PATH_LINK = "/link";
const PATH_CALENDAR = "/calendar";
const PATH_IO_LIST = "/io_list";
// const PATH_GRAPH = "/graph"; //Ruta Original
const PATH_TESTGRAPH = "/graph"; //Ruta de pruebas
const PATH_BINNACLE = "/binnacle";
const PATH_INDEX = "/dashboard";
const PATH_DASHBOARD = "/dashboard";
const PATH_TIMELAPSE = "/timelapse";
const PATH_TIMELAPSE_ID = `${PATH_TIMELAPSE}/:crop`;
const PATH_TREECHART = "/system";
const PATH_CONFIG = "/config";
const PATH_CONFIG_ID = `${PATH_CONFIG}/:id`;
const PATH_INFO = "/info";
const PATH_NEWDATABYCSV = "/newdatabycsv";
const PATH_LOGIN = "/login";
const PATH_RIEGO = "/irrigation-routine";
const PATH_SETTINGS = "/profile-settings";
const PATH_SATELLITE = "/satellite-analysis";
const PATH_WATERTANK = "/water-tank";
const PATH_CAMERA = "/camera-testing";

const ALL_PATHS = {
  PATH_ADMIN,
  PATH_LINK,
  PATH_CALENDAR,
  PATH_IO_LIST,
  // PATH_GRAPH,
  PATH_TESTGRAPH,
  PATH_BINNACLE,
  PATH_INDEX,
  PATH_DASHBOARD,
  PATH_CONFIG,
  PATH_CONFIG_ID,
  PATH_INFO,
  PATH_LOGIN,
  PATH_TIMELAPSE,
  PATH_TIMELAPSE_ID,
  PATH_TREECHART,
  PATH_NEWDATABYCSV,
  PATH_RIEGO,
  PATH_SETTINGS,
  PATH_SATELLITE,
  PATH_WATERTANK,
  PATH_CAMERA,
};

const LISTAS_DATA = [
  {
    path: PATH_SETTINGS,
    ComponentIcon: SettingsApplicationsRounded,
    text: "Ajustes",
  },
  // {
  //   path: PATH_ADMIN,
  //   ComponentIcon: FormatListNumbered,
  //   text: "Tareas",
  // },
  {
    path: PATH_LINK,
    ComponentIcon: VpnKey,
    text: "Token",
  },
  {
    path: PATH_CALENDAR,
    ComponentIcon: InsertInvitation,
    text: "Calendario",
  },
  {
    path: PATH_IO_LIST,
    ComponentIcon: Power,
    text: "Entradas/Salidas",
  },
  // {
  //   path: PATH_TREECHART,
  //   ComponentIcon: AccountTree,
  //   text: "Sistema",
  // },
  {
    path: PATH_BINNACLE,
    ComponentIcon: ImportContacts,
    text: "Bitácora",
  },
  // {
  //   path: PATH_GRAPH,
  //   ComponentIcon: Timeline,
  //   text: "Gráficas-Orginal",
  // },
  {//Graficas en Test
    path: PATH_TESTGRAPH,
    ComponentIcon: Timeline,
    text: "Gráficas",
  },
  {
    path: PATH_RIEGO,
    ComponentIcon: Waves,
    text: "Rutina de Riego",
  },
  {
    path: PATH_SATELLITE,
    ComponentIcon: Satellite,
    text: "Análisis por satélite",
  },
  {
    path: PATH_WATERTANK,
    ComponentIcon: WaterTankIcon,
    text: "Operar Tanques",
  },
  {
    path: PATH_CAMERA,
    ComponentIcon: Videocam,
    text: "Videocamara",
  },
  /*{
        path:PATH_NEWDATABYCSV,
        ComponentIcon:PublishIcon,
        text:'Subir Lecturas',
    },*/
];

const RUTA_PRIVATE_DATA = [
  // { path: PATH_GRAPH, ComponentReact: Graph },
  { path: PATH_TESTGRAPH, ComponentReact: GraphImproved },
  { path: PATH_ADMIN, ComponentReact: Tareas },
  { path: PATH_TREECHART, ComponentReact: TreeChart },
  { path: PATH_DASHBOARD, ComponentReact: DashboardWCtx },
  { path: PATH_CONFIG_ID, ComponentReact: ConfigIO },
  { path: PATH_LINK, ComponentReact: Token },
  { path: PATH_INFO, ComponentReact: InfoUser },
  { path: PATH_IO_LIST, ComponentReact: IOList },
  { path: PATH_BINNACLE, ComponentReact: Manual },
  { path: PATH_CALENDAR, ComponentReact: Calendar },
  { path: PATH_TIMELAPSE_ID, ComponentReact: Timelapse },
  { path: PATH_INDEX, ComponentReact: Crop },
  { path: PATH_NEWDATABYCSV, ComponentReact: NewDataByCSV },
  { path: PATH_RIEGO, ComponentReact: IrrigationForm },
  { path:PATH_SETTINGS, ComponentReact: ProfileSettings },
  { path:PATH_SATELLITE, ComponentReact: SatelliteAnalysis },
  { path:PATH_WATERTANK, ComponentReact: WaterTankForm },
  { path:PATH_CAMERA, ComponentReact: CameraTesting },
];

export { LISTAS_DATA, RUTA_PRIVATE_DATA, ALL_PATHS };
