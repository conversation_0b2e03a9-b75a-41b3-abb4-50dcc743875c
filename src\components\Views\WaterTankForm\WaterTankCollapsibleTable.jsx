import React, { useState, useContext, useEffect, useCallback } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import {
  Box,
  Collapse,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Paper,
  Button,
  withStyles,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  Snackbar,
  SnackbarContent,
} from '@material-ui/core';
import KeyboardArrowDownIcon from '@material-ui/icons/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@material-ui/icons/KeyboardArrowUp';
import EditIcon from '@material-ui/icons/Edit';
import DeleteIcon from '@material-ui/icons/Delete';
import PlayArrowIcon from '@material-ui/icons/PlayArrow';
import StopIcon from '@material-ui/icons/Stop';
import CheckCircleIcon from '@material-ui/icons/CheckCircle';
import { UserContext } from '../../../context/UserProvider';
import moment from 'moment';
import 'moment/locale/es';
import { db } from '../../../config/firebase';
import { get_Uid_Sate_ForSwitch } from '../../../context/functions/DashboardFunctions/divideUIDByKind';
import { IN_SWITCH } from '../../../constants/globalConst';
import { keys } from 'highcharts';

const StyledTableCell = withStyles((theme) => ({
  head: {
    backgroundColor: "#3B3C43",
    color: " #FFFFFF",
  },
  body: {
    fontSize: 14,
  },
}))(TableCell);

// Estilos para la tabla
const useStyles = makeStyles((theme) => ({
  root: {
    width: '100%',
    marginTop: theme.spacing(3),
  },
  table: {
    minWidth: 650,
  },
  tableContainer: {
    marginBottom: theme.spacing(2),
  },
  actionButton: {
    marginRight: theme.spacing(1),
  },
  detailsTable: {
    marginBottom: theme.spacing(2),
  },
}));

// Función para obtener el estado actual de un switch específico
const getSwitchState = async (switchUid, usuario) => {
  if (!switchUid || !usuario || !usuario.username) {
    console.log("No hay switch configurado o usuario no disponible");
    return null;
  }

  try {
    // Extraer el nodeId, canId y outId del UID del switch (formato: macId@canId@kind@outId)
    const switchParts = switchUid.split('@');
    if (switchParts.length < 4) {
      console.error("Formato de UID de switch inválido:", switchUid);
      return null;
    }

    const macId = switchParts[0];
    const canId = switchParts[1];
    const outId = switchParts[3];

    // Obtener los datos del switch desde Firebase
    const docPath = `${usuario.username}/infoDevices/${macId}/${canId}/fromModule/render`;
    const docRef = await db.doc(docPath).get();

    if (!docRef.exists) {
      console.error("No se encontraron datos para el switch:", switchUid);
      return null;
    }

    const switchData = docRef.data().C_bool;
    const resp = switchData[Number(outId)];

    return resp;
  } catch (error) {
    console.error("Error al obtener el estado del switch:", error);
    return null;
  }
};

// Función para obtener el nivel actual de agua de un tanque
const getWaterLevelData = async (sensorUid, usuario) => {
  if (!sensorUid || !usuario || !usuario.username) {
    console.log("No hay sensor de nivel configurado para este tanque");
    return null;
  }

  try {
    // Extraer el nodeId y sensorId del UID del sensor (formato: nodeId@sensorId)
    const sensorParts = sensorUid.split('@');
    if (sensorParts.length < 4) {
      console.error("Formato de UID de sensor inválido:", sensorUid);
      return null;
    }

    const nodeId = sensorParts[2];
    const sensorId = sensorParts[4];

    // Obtener los datos del sensor
    const docPath = `${usuario.username}/loraDevices/nodes/${nodeId}/sensors/dataSensors`;
    const docRef = await db.doc(docPath).get();

    if (!docRef.exists) {
      console.error("No se encontraron datos para el sensor:", sensorUid);
      return null;
    }

    const sensorData = docRef.data();
    if (!sensorData || !sensorData.data) {
      console.error("Estructura de datos inválida para el sensor:", sensorUid);
      return null;
    }

    // Buscar el sensor específico en el array de datos
    const sensorInfo = sensorData.data.find(sensor => sensor.id === Number(sensorId));
    if (!sensorInfo) {
      console.error("No se encontró información para el sensor ID:", sensorId);
      return null;
    }

    // Determinar el valor a usar según el tipo de medición
    let waterLevelPercentage;
    let waterLevelLiters;
    if(sensorInfo.stateSensor === "available") {
      waterLevelPercentage = sensorInfo.percentageWater;
      waterLevelLiters = sensorInfo.litersWater;
    } else {
      waterLevelPercentage = -1;
      waterLevelLiters = -1;
    }

    return { percentage: waterLevelPercentage, liters: waterLevelLiters};
  } catch (error) {
    console.error("Error al obtener datos del nivel de agua:", error);
    return null;
  }
};

// Función para obtener el volumen de un contenedor de agua
const getContainerWaterVolume = async (containerUid, usuario) => {
  if(!containerUid || !usuario || !usuario.username) {
    console.log("No hay datos del volumen del tanque");
    return null;
  }
  try {
    // Extraer el nodeId y sensorId del UID del sensor (formato: nodeId@sensorId)
    const sensorParts = containerUid.split('@');
    if (sensorParts.length < 4) {
      console.error("Formato de UID de sensor inválido:", containerUid);
      return null;
    }
    const nodeId = sensorParts[2];
    const sensorId = sensorParts[4];

    // Obtener los datos del sensor
    const docPath = `${usuario.username}/loraDevices/nodes/${nodeId}/waterContainers/waterTankData`;
    const docRef = await db.doc(docPath).get();
    if(docRef.exists) {
      const sensorData = docRef.data().data;
      const containerData = sensorData.find(container => container.id === sensorId);
      if(containerData) {
        return containerData.capacity;
      } else {
        return null;
      }
    }

  } catch (error) {
    console.error("Error en obtener los datos del volumen del tanque:",error)
  }
};

// Función para validar si se puede ejecutar una acción en un tanque
const validateTankAction = async (tank, usuario, levelSensorsUid, levelSensorsNames, onShowAlert, handleAlert) => {
  const fillData = tank.fillData;
  const emptyData = tank.emptyData;
  const maxLevelSwitchUid = fillData?.maxSwitch;
  const minLevelSwitchUid = emptyData?.minSwitch;
  let maxSwitchStateSaved = null;

  try {
    // Verificar el estado de los switches según la acción a realizar
    if (tank.action === "fill") {
      // Para llenado, verificar que el switch de nivel máximo no esté activado (1)
      if (maxLevelSwitchUid) {
        const maxSwitchState = await getSwitchState(maxLevelSwitchUid, usuario);
        maxSwitchStateSaved = maxSwitchState;

        if (maxSwitchState === "1") {
          // Si el switch de nivel máximo está activado, mostrar alerta y no ejecutar la acción
          // if (onShowAlert) {
          //   onShowAlert(`No se puede ejecutar el llenado en ${tank.name} porque el sensor de nivel máximo está activado.`);
          // }
          handleAlert(
            true,
            "warning",
            "Operación no permitida",
            `No se puede ejecutar el llenado en ${tank.name} porque el sensor de nivel máximo está activado.`
          );

          return false; // No se puede ejecutar la acción

        }
      }

      const waterSourceUid = fillData.waterSourceLevelSensor;
      const waterSourceIndex = levelSensorsUid.findIndex(uid => uid === waterSourceUid);
      const waterSourceName = waterSourceUid !== null ? levelSensorsNames[waterSourceIndex] : null;

      if(waterSourceUid !== null && tank.waterLevelSensor !== null) {
        const waterLevelSource = await getWaterLevelData(waterSourceUid, usuario);
        const waterLevelTank = await getWaterLevelData(tank.waterLevelSensor, usuario);
        const waterCapacityFillTank = await getContainerWaterVolume(tank.waterLevelSensor, usuario);

        // console.log("Esto es waterLevelSource",waterLevelSource);
        // console.log("Esto es waterCapacityFillTank",waterCapacityFillTank);

        if(waterLevelTank.liters !== -1 && waterCapacityFillTank !== 0) {
          if(waterLevelTank.percentage >= 100 && maxSwitchStateSaved === "0") {
            handleAlert(
              true,
              "warning",
              "Operación no permitida",
              `No se puede ejecutar el llenado en ${tank.name} porque el nivel de agua detectado es de ${waterLevelTank.percentage}%. Y el sensor de nivel máximo no se encuentra activado. Revisa el sensor de nivel máximo `
            );
            return false; // No se puede ejecutar la acción
          }else if(waterLevelTank.percentage >= 99){
            handleAlert(
              true,
              "warning",
              "Operación no permitida",
              `No se puede ejecutar el llenado en ${tank.name} porque el nivel de agua detectado es de ${waterLevelTank.percentage}%. El nivel debe ser menor al 99%.`
            );
            return false; // No se puede ejecutar la acción
          }
        }


        if(waterLevelSource !== null && waterLevelSource.liters !== -1) {
          if(waterCapacityFillTank && waterCapacityFillTank !== 0) {
            if (waterLevelSource.liters < waterCapacityFillTank) {
              // if (onShowAlert) {
              //   onShowAlert(`No se puede ejecutar el llenado en ${tank.name} porque el tanque de origen "${waterSourceName}" no tiene suficiente agua.`);
              // }
              handleAlert(
                true,
                "warning",
                "Operación no permitida",
                `No se puede ejecutar el llenado en ${tank.name} porque el tanque de origen "${waterSourceName}" no tiene suficiente agua.`
              );
              return false; // No se puede ejecutar la acción
            }
          } else {
            // if (onShowAlert) {
            //   onShowAlert(`No se puede ejecutar el llenado en ${tank.name} porque se desconoce su volumen máximo. Completa la información del tanque en la configuración de la card.`);
            // }
            handleAlert(
              true,
              "warning",
              "Operación no permitida",
              `No se puede ejecutar el llenado en ${tank.name} porque se desconoce su volumen máximo. Completa la información del tanque en la configuración de la card.`
            );
            return false; // No se puede ejecutar la acción
          }
        } else {
          // if (onShowAlert) {
          //   onShowAlert(`No se puede ejecutar el llenado en ${tank.name} porque se desconoce el nivel del tanque de origen "${waterSourceName}". Revisar sensor y/o configuración de la card.`);
          // }
          handleAlert(
              true,
              "warning",
              "Operación no permitida",
              `No se puede ejecutar el llenado en ${tank.name} porque se desconoce el nivel del tanque de origen "${waterSourceName}". Revisar conexión del sensor y/o configuración de la card.`
            );
          return false; // No se puede ejecutar la acción
        }
      }
    } else if (tank.action === "empty") {
      // Para vaciado, verificar que el switch de nivel mínimo no esté desactivado (0)
      if (minLevelSwitchUid) {
        const minSwitchState = await getSwitchState(minLevelSwitchUid, usuario);

        if (minSwitchState === "0") {
          // Si el switch de nivel mínimo está desactivado, mostrar alerta y no ejecutar la acción
          // if (onShowAlert) {
          //   onShowAlert(`No se puede ejecutar el vaciado en ${tank.name} porque el sensor de nivel mínimo está desactivado.`);
          // }
          handleAlert(
            true,
            "warning",
            "Operación no permitida",
            `No se puede ejecutar el vaciado en ${tank.name} porque el sensor de nivel mínimo está desactivado.`
          );
          return false; // No se puede ejecutar la acción
        }
      }

      // Verificar el nivel de agua del tanque de destino (targetTank) para la acción de vaciar
      const targetTankUid = emptyData.targetTankWaterLevelS;
      const targetTankIndex = levelSensorsUid.findIndex(uid => uid === targetTankUid);
      const targetTankName = targetTankUid !== null ? levelSensorsNames[targetTankIndex] : null;

      if (targetTankUid !== null) {
        // Obtener el nivel actual de agua del tanque de destino
        const targetWaterLevel = await getWaterLevelData(targetTankUid, usuario);
        const targetWaterCapacity = await getContainerWaterVolume(targetTankUid, usuario);
        const maxCapacityOfTank = await getContainerWaterVolume(tank.waterLevelSensor, usuario);

        const TotalEstimatedWaterVolume = targetWaterLevel.liters + maxCapacityOfTank;

        if (targetWaterLevel && targetWaterLevel.liters !== -1 ) {
          if(targetWaterCapacity && targetWaterCapacity !== 0) {
            if(maxCapacityOfTank && maxCapacityOfTank !== 0){
              if(TotalEstimatedWaterVolume > targetWaterCapacity) { //Si el nivel de agua supera a la capacidad del tanque de destino, no se puede ejecutar la acción de vaciado
                const minimumCapacityToReceiveWater = Number(targetWaterCapacity) - Number(maxCapacityOfTank);
                const minimumPercentage = (minimumCapacityToReceiveWater * 100) / Number(targetWaterCapacity);
                // if (onShowAlert) {
                //   onShowAlert(`No se puede ejecutar el vaciado en ${tank.name} porque el tanque de destino "${targetTankName}" está demasiado lleno (${targetWaterLevel.percentage}%). El nivel debe ser menor o igual al ${minimumPercentage.toFixed(0)}%.`);
                // }
                handleAlert(
                  true,
                  "warning",
                  "Operación no permitida",
                  `No se puede ejecutar el vaciado en ${tank.name} porque el tanque de destino "${targetTankName}" está demasiado lleno (${targetWaterLevel.percentage}%). El nivel debe ser menor o igual al ${minimumPercentage.toFixed(0)}%.`
                );
                return false; // No se puede ejecutar la acción
              }
            } else {
              // if (onShowAlert) {
              //   onShowAlert(`No se puede ejecutar el vaciado en ${tank.name} porque se desconoce su volumen máximo. Es necesario configurar los datos en el sensor de nivel en el tanque que se quiere vaciar.`);
              // }
              handleAlert(
                true,
                "warning",
                "Operación no permitida",
                `No se puede ejecutar el vaciado en ${tank.name} porque se desconoce su volumen máximo. Es necesario configurar los datos en el sensor de nivel en el tanque que se quiere vaciar.`
              );
              return false; // No se puede ejecutar la acción
            }
          } else  {
            // if (onShowAlert) {
            //   onShowAlert(`No se puede ejecutar el vaciado en ${tank.name} porque se desconoce el volumen máximo del tanque de destino "${targetTankName}". Es necesario configurar los datos del sensor de nivel en el tanque de destino.`);
            // }
            handleAlert(
              true,
              "warning",
              "Operación no permitida",
              `No se puede ejecutar el vaciado en ${tank.name} porque se desconoce el volumen máximo del tanque de destino "${targetTankName}". Es necesario configurar los datos del sensor de nivel en el tanque de destino.`
            );
            return false; // No se puede ejecutar la acción
          }
        } else if(targetWaterLevel && targetWaterLevel === -1) {
          // if (onShowAlert) {
          //   onShowAlert(`No se puede ejecutar el vaciado en ${tank.name} porque se desconoce el nivel del tanque de destino "${targetTankName}". Es necesario configurar los datos del sensor de nivel en el tanque de destino.`);
          // }
          handleAlert(
            true,
            "warning",
            "Operación no permitida",
            `No se puede ejecutar el vaciado en ${tank.name} porque se desconoce el nivel del tanque de destino "${targetTankName}". Es necesario configurar los datos del sensor de nivel en el tanque de destino.`
          );
          return false; // No se puede ejecutar la acción
        }
      }
    } else if(tank.action === "recirculate") {
      if (minLevelSwitchUid) {
        const minSwitchState = await getSwitchState(minLevelSwitchUid, usuario);
        if (minSwitchState === "0") {
          handleAlert(
            true,
            "warning",
            "Operación no permitida",
            `No se puede ejecutar la recirculación en ${tank.name} porque el sensor de nivel mínimo está desactivado.`
          );
          return false; // No se puede ejecutar la acción
        }
      }
      const waterLevel = await getWaterLevelData(tank.waterLevelSensor, usuario);
      if (waterLevel && waterLevel.percentage !== -1) {
        if(waterLevel.percentage <= 25) {
          handleAlert(
            true,
            "warning",
            "Operación no permitida",
            `No se puede ejecutar la recirculación en ${tank.name} porque el nivel de agua detectado es de ${waterLevel.percentage}%. El nivel debe ser mayor al 25%.`
          );
          return false; // No se puede ejecutar la acción
        }
      }


    }

    // Verificar el sensor de nivel del tanque actual
    if (tank.waterLevelSensor) {
      const waterLevel = await getWaterLevelData(tank.waterLevelSensor, usuario);
      const waterLevelSensorIndex = levelSensorsUid.findIndex(uid => uid === tank.waterLevelSensor);
      const waterLevelSensorName = levelSensorsNames[waterLevelSensorIndex];

      if (!waterLevel || waterLevel.percentage === -1) {
        // if (onShowAlert) {
        //   onShowAlert(`La acción ${tank.action} en ${tank.name} no se pudo completar debido a que no se tiene información del sensor de nivel ${waterLevelSensorName}.`);
        // }
        handleAlert(
          true,
          "warning",
          "Operación no permitida",
          `La acción ${tank.action} en ${tank.name} no se pudo completar debido a que no se tiene información del sensor de nivel ${waterLevelSensorName}.`
        );
        return false; // No se puede ejecutar la acción
      }
    } else {
      // if (onShowAlert) {
      //   onShowAlert(`No se puede ejecutar la acción ${tank.action} en ${tank.name} porque no hay sensor de nivel configurado.`);
      // }
      handleAlert(
        true,
        "warning",
        "Operación no permitida",
        `No se puede ejecutar la acción ${tank.action} en ${tank.name} porque no hay sensor de nivel configurado.`
      );
      return false; // No se puede ejecutar la acción
    }

    return true; // Se puede ejecutar la acción
  } catch (error) {
    console.error("Error en la validación del tanque:", error);
    // if (onShowAlert) {
    //   onShowAlert(`Error al validar la operación en ${tank.name}. Inténtalo de nuevo.`);
    // }
    handleAlert(
      true,
      "error",
      "No se puede ejecutar la acción",
      `Error al validar la operación en ${tank.name}. Inténtalo de nuevo.`
    );
    return false; // No se puede ejecutar la acción por error
  }
};

// Componente para cada fila de la tabla
function Row(props) {
  const { row, handleExecute, handleEdit, handleDelete, isExecuting, onShowAlert, isAnyTankExecuting, isRecirculating, isAnyTankRecirculating, handleAlert } = props;
  const [open, setOpen] = useState(false);
  const [actionSelected, setActionSelected] = useState(row.action === "fill" ? 1 : (row.action === "empty" ? 0 : (row.action === "recirculate" ? 3 : 1)))
  const [maxSwitchState, setMaxSwitchState] = useState(null);
  const [minSwitchState, setMinSwitchState] = useState(null);
  const [buttonDisabled, setButtonDisabled] = useState(false);
  const [tooltipText, setTooltipText] = useState("");
  const [initialSwitchState, setInitialSwitchState] = useState(null);
  const [operationCompleted, setOperationCompleted] = useState(false);

  const classes = useStyles();
  const { userTimezone, usuario, levelSensorsUid, levelSensorsNames } = useContext(UserContext);

  // Función para actualizar el estado del botón según la acción y el estado de los switches
  const updateButtonState = useCallback((action, maxState, minState) => {
    if (isExecuting) {
      // Si está ejecutando, el botón siempre está habilitado para poder detener
      setButtonDisabled(false);
      setTooltipText("");
      return;
    }

    // Nueva lógica: separar recirculación de llenado/vaciado
    if (action === "recirculate") {
      // Para recirculación, solo verificar si hay otra recirculación en curso
      if (isAnyTankRecirculating && !isRecirculating) {
        setButtonDisabled(true);
        setTooltipText("No se puede ejecutar porque hay otro tanque recirculando");
        return;
      }
      // Verificar solo el switch de nivel mínimo para recirculación
      if (minState === "0") {
        setButtonDisabled(true);
        setTooltipText("No se puede recircular porque el sensor de nivel mínimo está desactivado");
      } else {
        setButtonDisabled(false);
        setTooltipText("");
      }
    } else {
      // Para llenado y vaciado, mantener exclusividad mutua
      if (isAnyTankExecuting && !isExecuting) {
        setButtonDisabled(true);
        setTooltipText("No se puede ejecutar porque hay otro tanque llenando o vaciando");
        return;
      }

      if (action === "fill" && maxState === "1") {
        setButtonDisabled(true);
        setTooltipText("No se puede llenar porque el sensor de nivel máximo está activado");
      } else if (action === "empty" && minState === "0") {
        setButtonDisabled(true);
        setTooltipText("No se puede vaciar porque el sensor de nivel mínimo está desactivado");
      } else {
        setButtonDisabled(false);
        setTooltipText("");
      }
    }
  }, [isExecuting, isAnyTankExecuting, isRecirculating, isAnyTankRecirculating, setButtonDisabled, setTooltipText]);

  // Función para mostrar alerta cuando se completa la operación
  const showCompletionAlert = useCallback((action) => {
    const actionText = action === "fill" ? "llenado" : (action === "empty" ? "vaciado" : "recirculación");
    const message = `Operación de ${actionText} completada para ${row.name}`;

    // Usar la función de alerta del componente padre
    if (onShowAlert) {
      onShowAlert(message);
    }
  }, [row.name, onShowAlert]);

  // Función para manejar la ejecución con validaciones
  const handleExecuteWithValidation = useCallback(async () => {
    // Si ya está ejecutándose, permitir detener sin validaciones
    if (isExecuting) {
      handleExecute(row, false, false, null);
      return;
    }

    // Si no está ejecutándose, ejecutar validaciones antes de cambiar el estado
    const canExecute = await validateTankAction(
      row,
      usuario,
      levelSensorsUid,
      levelSensorsNames,
      onShowAlert,
      handleAlert
    );

    // Solo ejecutar si pasa las validaciones
    if (canExecute) {
      handleExecute(row, false, false, null);
    }
    // Si no pasa las validaciones, no se ejecuta nada y el botón mantiene su estado
  }, [isExecuting, row, usuario, levelSensorsUid, levelSensorsNames, onShowAlert, handleExecute, handleAlert]);

  // Efecto para monitorear el estado de los switches
  useEffect(() => {
    const checkSwitchesState = async () => {
      // Obtener los UIDs de los switches de nivel máximo y mínimo
      const maxLevelSwitchUid = row.fillData?.maxSwitch;
      const minLevelSwitchUid = row.emptyData?.minSwitch;

      let maxState = null;
      let minState = null;

      // Verificar el estado de los switches
      if (maxLevelSwitchUid) {
        maxState = await getSwitchState(maxLevelSwitchUid, usuario);
        setMaxSwitchState(maxState);
      }

      if (minLevelSwitchUid) {
        minState = await getSwitchState(minLevelSwitchUid, usuario);
        setMinSwitchState(minState);
      }

      // Actualizar el estado del botón según la acción seleccionada y el estado de los switches
      const currentAction = actionSelected === 1 ? "fill" : (actionSelected === 0 ? "empty" : "recirculate");
      updateButtonState(currentAction, maxState, minState);

      // Monitorear cambios en el estado del switch para detectar finalización de operación
      if (isExecuting && !operationCompleted) {
        let relevantSwitchState;
        let expectedChange;

        if (currentAction === "recirculate") {
          // Para recirculación, solo monitorear el switch de nivel mínimo
          relevantSwitchState = minState;
          expectedChange = "0"; // Se detiene cuando el switch de nivel mínimo se desactiva
        } else {
          // Para llenado y vaciado, usar la lógica original
          relevantSwitchState = currentAction === "fill" ? maxState : minState;
          expectedChange = currentAction === "fill" ? "1" : "0";
        }

        // Si es la primera vez que se ejecuta después de iniciar la operación, guardar el estado inicial
        if (initialSwitchState === null) {
          setInitialSwitchState(relevantSwitchState);
        }
        // Si ya tenemos un estado inicial y el estado actual ha cambiado, la operación se ha completado
        else if (initialSwitchState !== relevantSwitchState) {
          // Verificar que el cambio sea el esperado según la operación
          if (relevantSwitchState === expectedChange) {
            setOperationCompleted(true);

            // Crear la fecha de finalización
            const completionTime = new Date().toISOString();
            console.log(`Operación completada por switch para ${row.name}. Fecha: ${completionTime}`);

            // Mostrar alerta de finalización
            showCompletionAlert(currentAction);

            // Notificar al componente padre que la operación ha terminado por un cambio en el Switch
            // Pasamos la fecha de finalización como parámetro adicional
            handleExecute(row, true, true, completionTime);
          }
        }
      }
    };

    // Verificar el estado inicial
    checkSwitchesState();

    // Configurar un intervalo para verificar periódicamente el estado de los switches
    const intervalId = setInterval(checkSwitchesState, 5000); // Verificar cada 5 segundos

    // Limpiar el intervalo cuando el componente se desmonte
    return () => clearInterval(intervalId);
  }, [row, actionSelected, usuario, updateButtonState, isExecuting, initialSwitchState, operationCompleted, showCompletionAlert, handleExecute]);

  // Resetear el estado de monitoreo cuando cambia el estado de ejecución
  useEffect(() => {
    if (!isExecuting) {
      setInitialSwitchState(null);
      setOperationCompleted(false);
    }
  }, [isExecuting]);

  const handleActionChange = (event, tankId) => {
    const newAction = event.target.value === 1 ? "fill" : (event.target.value === 0 ? "empty" : "recirculate");
    handleEdit({ ...row, action: newAction });
    setActionSelected(event.target.value);

    // Actualizar el estado del botón cuando cambia la acción
    updateButtonState(newAction, maxSwitchState, minSwitchState);
  };

  return (
    <React.Fragment>
      {/* Fila principal */}
      <TableRow>
        <TableCell>
          <IconButton
            aria-label="expand row"
            size="small"
            onClick={() => setOpen(!open)}
          >
            {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
          </IconButton>
        </TableCell>
        <TableCell component="th" scope="row">
          {row.name}
        </TableCell>
        <TableCell>
          <FormControl className={classes.formControl}>
            <Select
              labelId="demo-simple-select-label"
              id="demo-simple-select"
              value={actionSelected}
              onChange={(e) => handleActionChange(e, row.id)}
            >
              <MenuItem value={0}>Vaciar</MenuItem>
              <MenuItem value={1}>Llenar</MenuItem>
              <MenuItem value={3}>Recircular</MenuItem>
              
            </Select>
          </FormControl>
        </TableCell>
        <TableCell>
          {row.lastExecution !== "Never" ? moment(row.lastExecution).tz(userTimezone || 'America/Mexico_City').format('DD/MM/YYYY, HH:mm [Hrs.]') : 'Nunca'}
        </TableCell>
        <TableCell>
          <Tooltip title={tooltipText} placement="top" arrow>
            <span>
              <Button
                variant="contained"
                color={isExecuting ? "secondary" : "primary"}
                size="small"
                startIcon={isExecuting ? <StopIcon /> : <PlayArrowIcon />}
                onClick={handleExecuteWithValidation}
                data-tank-id={row.id}
                disabled={buttonDisabled && !isExecuting}
              >
                {isExecuting ? 'Detener' : 'Ejecutar'}
              </Button>
            </span>
          </Tooltip>
        </TableCell>
        <TableCell>
          <IconButton
            aria-label="editar"
            className={classes.actionButton}
            onClick={() => {
              // Pasamos el objeto row sin la propiedad action para diferenciar
              // entre cambio de acción y edición completa
              const { action, ...tankWithoutAction } = row;
              handleEdit(tankWithoutAction);
            }}
          >
            <EditIcon />
          </IconButton>
          <IconButton
            aria-label="eliminar"
            onClick={() => handleDelete(row)}
          >
            <DeleteIcon />
          </IconButton>
        </TableCell>
      </TableRow>

      {/* Fila de detalles (expandible) */}
      <TableRow key={`${row.id}-details-row`}>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={6}>
          <Collapse in={open} timeout="auto" unmountOnExit>
            <Box margin={1}>
              <Typography variant="h6" gutterBottom component="div">
                Detalles de Configuración
              </Typography>
              <Table size="small" className={classes.detailsTable}>
                {/* <TableHead>
                  <TableRow>
                    <TableCell>Parámetro</TableCell>
                    <TableCell>Valor</TableCell>
                  </TableRow>
                </TableHead> */}
                <TableBody>
                  {row.details && Object.entries(row.details).map(([key, value], index) => (
                    <TableRow key={`${row.id}-detail-${index}`}>
                      <TableCell component="th" scope="row">
                        {key}
                      </TableCell>
                      <TableCell>{value}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>
    </React.Fragment>
  );
}

// Componente principal de la tabla
export default function WaterTankCollapsibleTable({ tanks, onExecute, onEdit, onDelete, handleAlert }) {
  const classes = useStyles();
  const [executingTankId, setExecutingTankId] = useState(null);
  const [recirculatingTankId, setRecirculatingTankId] = useState(null);
  // Estado para manejar las alertas a nivel de la tabla
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState("");

  const handleExecute = (tank, completed = false, stoppedBySwitch = false, completionTime = null) => {
    if (onExecute) {
      // Llamar a la función de ejecución del componente padre
      onExecute(tank, completed, stoppedBySwitch, completionTime);

      // Si la operación se completó o se detuvo, resetear el estado de ejecución correspondiente
      if (completed || stoppedBySwitch) {
        if (tank.action === "recirculate") {
          setRecirculatingTankId(null);
        } else {
          setExecutingTankId(null);
        }
      } else {
        // Manejar el inicio/detención según el tipo de operación
        if (tank.action === "recirculate") {
          // Para recirculación, manejar independientemente
          if (recirculatingTankId === tank.id) {
            setRecirculatingTankId(null);
          } else {
            setRecirculatingTankId(tank.id);
          }
        } else {
          // Para llenado y vaciado, mantener exclusividad mutua
          if (executingTankId === tank.id) {
            setExecutingTankId(null);
          } else {
            // Solo permitir si no hay otro tanque de llenado/vaciado ejecutándose
            if (executingTankId === null) {
              setExecutingTankId(tank.id);
            }
          }
        }
      }
    };
  };

  const handleEdit = (tank) => {
    if (onEdit) onEdit(tank);
  };

  const handleDelete = (tank) => {
    if (onDelete) onDelete(tank);
  };

  // Función para mostrar alertas desde los componentes Row
  const showCompletionAlert = (message) => {
    setAlertMessage(message);
    setShowAlert(true);

    // Cerrar la alerta después de 5 segundos
    setTimeout(() => {
      setShowAlert(false);
    }, 5000);
  };

  // Estilo para la alerta de éxito
  const successAlertStyle = {
    backgroundColor: '#4caf50',
    color: 'white',
    display: 'flex',
    alignItems: 'center',
  };

  return (
    <React.Fragment>
      <TableContainer component={Paper} className={classes.tableContainer}>
        <Table className={classes.table} aria-label="collapsible table">
          <TableHead>
            <TableRow>
              <StyledTableCell />
              <StyledTableCell align="left">Tanques</StyledTableCell>
              <StyledTableCell align="left">Acción</StyledTableCell>
              <StyledTableCell align="left">Última ejecución</StyledTableCell>
              <StyledTableCell align="left">Ejecutar</StyledTableCell>
              <StyledTableCell align="left">Editar/Eliminar</StyledTableCell>
              {/* <TableCell>Acción</TableCell>
              <TableCell>Última ejecución</TableCell>
              <TableCell>Ejecutar</TableCell>
              <TableCell>Editar/Eliminar</TableCell> */}
            </TableRow>
          </TableHead>
          <TableBody>
            {tanks && tanks.map((tank) => (
              <Row
                key={tank.id}
                row={tank}
                handleExecute={handleExecute}
                handleEdit={handleEdit}
                handleDelete={handleDelete}
                isExecuting={tank.action === "recirculate" ? recirculatingTankId === tank.id : executingTankId === tank.id}
                isAnyTankExecuting={executingTankId !== null}
                isRecirculating={recirculatingTankId === tank.id}
                isAnyTankRecirculating={recirculatingTankId !== null}
                onShowAlert={showCompletionAlert}
                handleAlert={handleAlert}
              />
            ))}
            {(!tanks || tanks.length === 0) && (
              <TableRow>
                <TableCell colSpan={6} align="center">
                  No hay tanques configurados
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Alerta de operación completada - ahora fuera del TableBody */}
      <Snackbar
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        open={showAlert}
        autoHideDuration={5000}
        onClose={() => setShowAlert(false)}
      >
        <SnackbarContent
          style={successAlertStyle}
          message={
            <span style={{ display: 'flex', alignItems: 'center' }}>
              <CheckCircleIcon style={{ marginRight: '8px' }} />
              {alertMessage}
            </span>
          }
        />
      </Snackbar>
    </React.Fragment>
  );
}
