# Cloud Function: savePolygonData

## Descripción
Esta Cloud Function procesa y guarda los datos de polígonos enviados desde el componente `SelectCropField.jsx` cuando el usuario selecciona polígonos de un archivo KML/KMZ.

## Endpoint
```
https://us-central1-gapy-c999c.cloudfunctions.net/savePolygonData
```

## Método
POST

## Estructura de datos de entrada

```json
{
  "username": "string",
  "polygons": [
    {
      "polygonName": "string",
      "geoJsonData": {
        "type": "Feature",
        "geometry": {
          "type": "Polygon",
          "coordinates": [[[lng, lat], [lng, lat], ...]]
        },
        "properties": {
          "name": "string"
        }
      },
      "timestamp": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

## Estructura de datos de salida

```json
{
  "success": true,
  "message": "Polígonos guardados exitosamente",
  "savedCount": 3,
  "polygonIds": ["id1", "id2", "id3"]
}
```

## Implementación sugerida (Node.js)

```javascript
const functions = require('firebase-functions');
const admin = require('firebase-admin');

// Inicializar Firebase Admin si no está inicializado
if (!admin.apps.length) {
  admin.initializeApp();
}

const db = admin.firestore();

exports.savePolygonData = functions.https.onRequest(async (req, res) => {
  // Configurar CORS
  res.set('Access-Control-Allow-Origin', '*');
  res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.set('Access-Control-Allow-Headers', 'Content-Type');

  // Manejar preflight OPTIONS request
  if (req.method === 'OPTIONS') {
    res.status(204).send('');
    return;
  }

  // Solo permitir método POST
  if (req.method !== 'POST') {
    res.status(405).json({ error: 'Método no permitido' });
    return;
  }

  try {
    const { username, polygons } = req.body;

    // Validar datos de entrada
    if (!username || !polygons || !Array.isArray(polygons)) {
      res.status(400).json({ 
        error: 'Datos inválidos. Se requiere username y array de polygons' 
      });
      return;
    }

    if (polygons.length === 0) {
      res.status(400).json({ 
        error: 'El array de polígonos no puede estar vacío' 
      });
      return;
    }

    // Procesar y guardar cada polígono
    const savedPolygonIds = [];
    const batch = db.batch();

    for (let i = 0; i < polygons.length; i++) {
      const polygon = polygons[i];
      
      // Validar estructura del polígono
      if (!polygon.polygonName || !polygon.geoJsonData || !polygon.timestamp) {
        throw new Error(`Polígono ${i + 1} tiene datos incompletos`);
      }

      // Crear referencia del documento
      const addr = `${username}/satelliteAnalysis/cropPolygons`;
      const docRef = db.collection(addr).doc();
      
      // Preparar datos para guardar
      const polygonData = {
        name: polygon.polygonName,
        geoJsonData: polygon.geoJsonData,
        timestamp: polygon.timestamp,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        type: 'uploaded_polygon',
        source: 'kml_kmz_file'
      };

      // Agregar al batch
      batch.set(docRef, polygonData);
      savedPolygonIds.push(docRef.id);
    }

    // Ejecutar batch write
    await batch.commit();

    // También actualizar el documento principal de polígonos
    const mainPolygonRef = db.collection(`${username}/satelliteAnalysis/cropPolygons`).doc('polygonData');
    
    // Obtener datos existentes
    const existingData = await mainPolygonRef.get();
    let existingFeatures = [];
    
    if (existingData.exists && existingData.data().geoJsonData) {
      existingFeatures = existingData.data().geoJsonData.features || [];
    }

    // Agregar nuevos polígonos
    const newFeatures = polygons.map(p => p.geoJsonData);
    const allFeatures = [...existingFeatures, ...newFeatures];

    // Actualizar documento principal
    const mainGeoJsonData = {
      type: "FeatureCollection",
      features: allFeatures
    };

    await mainPolygonRef.set({ 
      geoJsonData: mainGeoJsonData,
      lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
      totalPolygons: allFeatures.length
    });

    // Respuesta exitosa
    res.status(200).json({
      success: true,
      message: 'Polígonos guardados exitosamente',
      savedCount: polygons.length,
      polygonIds: savedPolygonIds
    });

  } catch (error) {
    console.error('Error en savePolygonData:', error);
    res.status(500).json({
      success: false,
      error: 'Error interno del servidor',
      message: error.message
    });
  }
});
```

## Estructura de base de datos resultante

```
/{username}/satelliteAnalysis/cropPolygons/
├── polygonData (documento principal con todos los polígonos)
│   ├── geoJsonData: { type: "FeatureCollection", features: [...] }
│   ├── lastUpdated: timestamp
│   └── totalPolygons: number
├── {polygonId1} (documento individual del polígono 1)
│   ├── name: string
│   ├── geoJsonData: object
│   ├── timestamp: string
│   ├── createdAt: timestamp
│   ├── type: "uploaded_polygon"
│   └── source: "kml_kmz_file"
└── {polygonId2} (documento individual del polígono 2)
    └── ...
```

## Notas de implementación

1. **CORS**: La función debe manejar CORS para permitir requests desde el frontend
2. **Validación**: Validar todos los datos de entrada antes de procesarlos
3. **Batch operations**: Usar batch writes para operaciones múltiples eficientes
4. **Error handling**: Manejar errores apropiadamente y devolver mensajes útiles
5. **Timestamps**: Usar server timestamps para consistencia
6. **Estructura flexible**: Permitir extensión futura de la estructura de datos

## Deployment

Para desplegar esta función:

```bash
firebase deploy --only functions:savePolygonData
```

## Testing

Ejemplo de test con curl:

```bash
curl -X POST https://us-central1-gapy-c999c.cloudfunctions.net/savePolygonData \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "polygons": [{
      "polygonName": "Campo Test",
      "geoJsonData": {
        "type": "Feature",
        "geometry": {
          "type": "Polygon",
          "coordinates": [[[-100, 40], [-100, 41], [-99, 41], [-99, 40], [-100, 40]]]
        },
        "properties": {"name": "Campo Test"}
      },
      "timestamp": "2024-01-01T00:00:00.000Z"
    }]
  }'
```
