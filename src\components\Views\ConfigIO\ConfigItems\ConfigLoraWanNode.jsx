import React, { useContext, useEffect, useRef, useState } from 'react'
import { <PERSON><PERSON>r, Grid, TextField, Typography } from '@material-ui/core';
import { backGroundList } from '../../../../constants/globalConst';
import { UserContext } from '../../../../context/UserProvider';
import moment from 'moment';
import UpdatingButton from './ConfigComponents/UpdatingButton';
import DialogConfirm from './ConfigComponents/DialogConfirm';
import { db } from '../../../../config/firebase';
import { ConfigWaterLevel } from './ConfigNodeComponents/ConfigWaterLevel';

const allParameters = [
  { id:"conductivity", alias: "Conductividad(EC)"},
  { id:"temperature", alias: "Temperatura"},
  { id:"ph", alias: "pH"},
  { id:"humidity", alias: "Humedad"},
  { id:"pressure", alias: "Presión"},
  { id:"co2", alias: "CO2"},
  { id:"nitrogen", alias: "Nitrógen<PERSON>"},
  { id:"phosphorus", alias: "Fós<PERSON><PERSON>"},
  { id:"potassium", alias: "Potasio"},
  { id:"photosynthetic", alias: "Fotosistencia(PAR)"},
  { id:"windSpeed", alias: "Velocidad del viento"},
  { id:"rainGauge", alias: "Lluvia"},
  { id:"pm2.5", alias: "PM2.5"},
  { id:"pm10", alias: "PM10"},
  { id:"noise", alias: "Ruido"},
  { id:"rainfall", alias: "Lluvia"},
  { id:"salinity", alias: "Salinidad"},
]


export const ConfigLoraWanNode = (propiedades) => {
	const data = propiedades.data;
  const ide = data.ide;
  const arrayDeCadenas = ide.split("@");
  const typeOfSensor = arrayDeCadenas[3];
	//const nodeNumber = arrayDeCadenas[3];
  const macId = arrayDeCadenas[0]; 
	const { usuario, userTimezone } = useContext(UserContext);
  const [error, setError] = useState(null);
  const [openWindow, setOpenWindow] = useState(false);
  const [newName, setNewName] = useState('');
	const [nodeNumber, setNodeNumber] = useState("");
  const [lastUpdate, setLastUpdate] = useState("");
  const [nameDataSaved, setNameDataSaved] = useState([]);
  const [offsetParameters, setOffsetParameters] = useState([])
  const [dataConfig, setDataConfig] = useState({
      nodeName: "-",
      nodeId: "-",
      fecha: "-",
      offset_values: ["-", "-", "-", "-", "-", "-", "-"],
      offset_signs: ["0", "0", "0", "0", "0", "0", "0"],
  });
  // Referencia para exponer la función de guardado del hijo
  const containerDataFormRef = useRef(null);

	const handleNameChange = (e) => {
		setNewName(e.target.value);
	};

	const editar = async (e) => {
        e.preventDefault();
    
        if (!newName.trim()) {
          setError("Ingrese Nombre");
          return;
        }
        setError(null);
       
        setOpenWindow(true);
    };

	const agreeDialog = async () => {
        
        //setLoading(true)
        try {
          const arrayDeCadenas = ide.split("@");
          const nodeId = arrayDeCadenas[2];
          const sensorId = arrayDeCadenas[4];
          const savePath = `${usuario.username}/loraDevices/nodes/${nodeId}/namesOfCards`;
          const docRef = db.collection(savePath).doc("names");

          const allNames = [...nameDataSaved];
          allNames.forEach((element) => {
            if(element.id === Number(sensorId)) {
              element.lastUpdate = new Date().toUTCString();
              element.name = newName;
            }
          })
          
          // const nameObject = nameDataSaved.find(
          //   (element) => element.id === Number(typeSensor)
          // );
          // nameObject.name = newName;
          // nameObject.lastUpdate = new Date().toUTCString();
          await docRef.set({ allNames })

          if(typeOfSensor === "14" || typeOfSensor === "15" || typeOfSensor === "24"){
            await handleSaveWaterContainerData()
          }



          //const item = { ...dataConfig, fecha: Date.now(), nodeName: newName ,uid: ide };
        //   const addr = `${usuario.username}/loraDevices/nodes/${nodeNumber}/configNode`;
    
        //   await db.collection(addr).doc("renderData").set({ item });
          
          //Envio de datos por MQTT
        //   for (let index = 0; index < arraySensorsData.length; index++) {
        //     procesarDatosSecuencialmente([arraySensorsData[index]]);
        //   }

          //Reset of values that changed
        //   for (let index = 0; index < arraySensorsData.length; index++) {
        //     arraySensorsData[index].changed = false;
        //   } 
          
          //setDataConfig({...dataConfig, nodeName: newName});
          setOpenWindow(false);
        } catch (error) {
          console.log(error);
        }
    };

    // Llamamos a la función de guardado del hijo (expuesta vía forwardRef)
    const handleSaveWaterContainerData = async () => {
      if (containerDataFormRef.current) {
        await containerDataFormRef.current.saveContainerData();
      }
    };

    const handleChangeOffset = (e, index) => {
      const { value } = e.target;
      const updatedParameters = offsetParameters.map((item, idx) => {
        if (idx === index) {
          return { ...item, value: value };
        }
        return item;
      });
      setOffsetParameters(updatedParameters);
    };

    useEffect(() => {
      let isMounted = true; // Bandera para verificar si el componente está montado
  
      const obtenerDatos = async () => {
        try {
          const arrayDeCadenas = ide.split("@");
          const nodeId = arrayDeCadenas[2];
          const typeSensor = arrayDeCadenas[3];
          const sensorId = arrayDeCadenas[4];
  
          const addr = `${usuario.username}/loraDevices/nodes/${nodeId}/namesOfCards`;
          const addr2 = `${usuario.username}/loraDevices/nodes/${nodeId}/sensors`;
          const nodesAddr = `${usuario.username}/loraDevices/nodes`;
  
          const leerDatosDB = async () => {
            try {
              const docRef = db.collection(addr).doc("names");
              const docRef2 = db.collection(addr2).doc("recordedOffsets");
              const docSnap = await docRef.get();
              const docSnap2 = await docRef2.get();
              
              const nodeNumDoc = await db.collection(nodesAddr).doc(nodeId).get();
  
              if (docSnap.exists && nodeNumDoc.exists) {
                const dataItem = docSnap.data().allNames;
                const nodeNumberObtained = nodeNumDoc.data().node;                
                const currentName = dataItem.find(
                  (element) => element.id === Number(sensorId)
                );

                let matchedParameters = null;
                if(docSnap2.exists && (typeSensor !== "14" && typeSensor !== "15" && typeSensor !== "12")){
                  const allOffsetValues = docSnap2.data().offsetValues;
                  const foundOffset = allOffsetValues.find(element => element.id === Number(typeSensor));
                  if (foundOffset) {
                    matchedParameters = Object.keys(foundOffset)
                      .filter(key => key !== 'id')
                      .map(key => {
                        const foundAlias = allParameters.find(element => element.id === key);
                        const alias = foundAlias ? foundAlias.alias : "undefined";
                        return {
                          nameId: key,
                          alias: alias,
                          value: foundOffset[key]
                        }
                      });
                  }
                } else {
                  matchedParameters = []
                }

                if (isMounted) {
                  setNewName(currentName?.name); // Usa opcional chaining para evitar errores
                  setNodeNumber(nodeNumberObtained);
                  setLastUpdate(currentName?.lastUpdate);
                  setNameDataSaved([...dataItem]);
                  setOffsetParameters(matchedParameters)
                }
              } else {
                console.log("No se encontró el documento!");
              }
            } catch (error) {
              console.error("Error al obtener el documento:", error);
            }
          };
  
          await leerDatosDB();
        } catch (error) {
          console.error("Error en obtenerDatos:", error);
        }
      };
  
      obtenerDatos();
  
      return () => {
        isMounted = false; // Marca como desmontado en la limpieza
      };
    }, [ide, usuario.username]);

  return (
    <>
	<div className="container mt-3">
        <form onSubmit={editar}>
          <div className="row">
            <div className="col-12">
              <ul className="list-group">
                <li
                  className="list-group-item"
                  style={{ background: backGroundList }}
                >
                  <div className="row">
                    <div className=" col-10">
                      <h4 style={{ color: "white" }}>{newName}</h4>
                    </div>                   
                  </div>
                </li>
                <li className="list-group-item">

                  <div className="row">
                    <div className="col-6">Número de Nodo:</div>
                    <div className="col-6">{nodeNumber}</div>
                  </div>

                  <div className="row">
                    <div className="col-6">Nombre:</div>
                    <div className="col-6">
                      <input
                        type="text"
                        placeholder="Ingrese Nombre"
                        className="form-control mb-2"
                        onChange={handleNameChange}
                        value={newName}
                      ></input>
                    </div>
                  </div>

                  {dataConfig.fecha && (
                    <div className="row">
                      <div className="col-6">Última modificación:</div>
                      <div className="col-6">
                        {/* {moment(comp.fecha).format("llll")} */}
                        {/* {new Date(dataConfig.fecha).toString()} */}
                        {moment(lastUpdate).tz(userTimezone).format('ddd, D [de] MMM [de] YYYY, HH:mm [Hrs.]')}
                      </div>
                    </div>
                  )}

                  <div style={{ marginTop: '15px' }}>
                    <Divider />
                  </div>

                  {(typeOfSensor === "14" || typeOfSensor === "15" || typeOfSensor === "24") && (
                    <ConfigWaterLevel 
                    ref={containerDataFormRef} 
                    username={usuario.username} 
                    nodeId={ide.split("@")[2]} 
                    typeSensor={ide.split("@")[3]}
                    sensorId={ide.split("@")[4]}
                    />
                  )}
                  {/* Offset Aqui */}
                  {offsetParameters?.length !== 0 && (
                    <div style={{ marginBottom: '10px', textAlign:'center', marginTop:'10px', fontWeight: 'bold', fontSize: '20px'}}>
                      Offsets
                    </div>
                  )}

                  {offsetParameters?.map((item,index) => (
                    <Grid container spacing={2} key={index} alignItems="center" justifyContent="flex-start">
                      <Grid item container xs={12} sm={4} justifyContent="flex-start">
                        <Typography variant="body1">{item.alias}:</Typography>
                      </Grid>
                      <Grid item xs={10} sm="auto">
                        <TextField
                          key={index}
                          id={`input_${index}`}
                          name={item.nameId}
                          type="number"
                          variant="outlined"
                          size="small"
                          value={item.value}
                          onChange={(e) => handleChangeOffset(e, index)}
                        />
                      </Grid>
                    </Grid>
                  
                  ))}

                </li>
              </ul>
            </div>
          </div>

          <div className="row ">
            {
              //como operador ternario
              error && (
                <div className="col-12 mt-3">
                  <div className="alert alert-danger alert-block">{error}</div>
                </div>
              )
            }
          </div>

          <UpdatingButton type="submit" disabled={false} />
        </form>

        
    </div>
    <DialogConfirm
    open={openWindow}
    handleClose={() => setOpenWindow(false)}
    agreeDialog={agreeDialog}
    title={"¿Está seguro que quiere continuar con esta operación?"}
    text={
      "Se realizará un cambio en el nombre de la tarjeta de información del Nodo."
    }
    />
    
  </>
  )
}
