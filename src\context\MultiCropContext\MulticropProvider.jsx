import React, { useCallback, useContext, useEffect, useState } from 'react'
import { MultiCropContext } from './MultiCropContext'
import { UserContext } from '../UserProvider';
import { db } from '../../config/firebase';
import moment from 'moment-timezone';


export const MulticropProvider = ({ children }) => {
	const {usuario} = useContext(UserContext);
	const [namesOfCrops, setNamesOfCrops] = useState([]);
	const [actualNumberOfCrops, setActualNumberOfCrops] = useState(1)
	const [tabValue, setTabValue] = useState(() => {
		// Recuperar el valor de la pestaña guardada en localStorage o usar 0 como valor predeterminado
		const savedTabValue = localStorage.getItem('dashboardTabValue');
		console.log('Inicializando tabValue. Valor guardado en localStorage:', savedTabValue);

		// Si hay un valor guardado, convertirlo a número
		if (savedTabValue !== null) {
			const parsedValue = parseInt(savedTabValue, 10);
			// Asegurarse de que el valor sea válido (por ahora solo verificamos que sea >= 0)
			// La validación contra el número máximo de pestañas se hará en un efecto posterior
			const validValue = parsedValue >= 0 ? parsedValue : 0;
			console.log('Usando valor de localStorage para tabValue:', validValue);
			return validValue;
		}
		console.log('No hay valor guardado en localStorage, usando 0 como valor predeterminado');
		return 0;
	})
	const [allTilesTest, setAllTilesTest] = useState(() => {
		// Intentar recuperar la estructura de tiles de localStorage
		const savedTilesStructure = localStorage.getItem('dashboardTilesStructure');
		console.log('Inicializando allTilesTest. Estructura guardada en localStorage:', savedTilesStructure ? 'Encontrada' : 'No encontrada');

		if (savedTilesStructure) {
			try {
				const parsedTiles = JSON.parse(savedTilesStructure);
				console.log('Usando estructura de tiles de localStorage');
				return parsedTiles;
			} catch (error) {
				console.error('Error al parsear la estructura de tiles de localStorage:', error);
			}
		}

		console.log('Usando estructura de tiles vacía');
		return [[]];
	})

	// Estado para controlar si los datos están cargando
	const [isLoadingTiles, setIsLoadingTiles] = useState(true)
	const [hiddenCards, setHiddenCards] = useState([])
	const [weeksOfCrops, setWeeksOfCrops] = useState([])
	const [daysOfCrops, setDaysOfCrops] = useState([])
	const [timeZoneSelected, setTimeZoneSelected] = useState('')
	const [idOfCrops, setIdOfCrops] = useState([])
	const [openConfigNewCrop, setOpenConfigNewCrop] = useState(false);
	const [openEditWindow, setOpenEditWindow] = useState(false);
	const [placeOfCrops, setPlaceOfCrops] = useState([])
	const [crops, setCrops] = useState([])
	const [variantsOfCrop, setVariantsOfCrop] = useState([])
	const [phenoStages, setPhenoStages] = useState([])
	const [indoorSystems, setIndoorSystems] = useState([])
	const [soilTypes, setSoilTypes] = useState([])
	const [datesOfStages, setDatesOfStages] = useState([])
	const [currentCoordinates, setCurrentCoordinates] = useState([]);
	const [horizontalMenuFlag, setHorizontalMenuFlag] = useState(false)
	const [parametersDisplayStatus , setParametersDisplayStatus ] = useState([])
	const testingAddDeleteCropUrl = process.env.REACT_APP_TEST_ADD_DELETE_CROP;
	const addRemoveCropUrl = process.env.REACT_APP_ADD_DELETE_CROP;

	const sendToCRcrop = async (data) => {
		try {
			const dataToSend = JSON.stringify(data);
			const response = await fetch(testingAddDeleteCropUrl, {
				method: 'POST',
				headers: {
				'Content-Type': 'application/json',
				},
				body: dataToSend,
			});

			if (!response.ok) {
				throw new Error('Error en la solicitud: ' + response.statusText);
			}
			if(response.ok && data.action === "addCrop") {
				obtenerDatos();
			} else if(response.ok && data.action === "deleteCrop") {
				obtenerDatos();
				getHiddenCardsData();
			}
			return response.ok ? "allOK" : "error"
		} catch (error) {
			console.error("Error en el guardado de datos de Multicultivo:",error)
		}
	}

	const saveTimeZone = async() => {
		try {
			const docRef = db.collection(usuario.username).doc('multiCrop')
			const timeZone = timeZoneSelected
			await docRef.update({ timeZone })
		} catch (error) {
			console.error("Error en guardar zona horaria:",error)
		}
	}

	const getArrayOfUids = (dataArray) => {
		let uidsCol1 = [];
		let uidsCol2 = [];
		let uidsCol3 = [];
		for (let i = 1; i < dataArray.length; i++) {
			const columnData = dataArray[i].map((item) => {
				return item.kind !== "ghost" ? item.uid : "ghost"
			})
			const columnDataFiltered = columnData.filter((item) => item !== "ghost");
			switch (i) {
				case 1:
					uidsCol1 = columnDataFiltered
					break;
				case 2:
					uidsCol2 = columnDataFiltered
					break;
				case 3:
					uidsCol3 = columnDataFiltered
					break;
				default:
					break;
			}
		}
		return {col_1: uidsCol1,col_2: uidsCol2, col_3: uidsCol3}
	}

	const saveNewTilesPosition = async() => {
		try {
			for (let index = 0; index < actualNumberOfCrops; index++) {
				const addr = `${usuario.username}/multiCrop/${idOfCrops[index]}`;
				const docRef = db.collection(addr).doc('cards');
				const render = getArrayOfUids(allTilesTest[index]);
				await docRef.set({ render })
			}

			const addressHiddenTiles = `${usuario.username}/multiCrop/hiddenCards`;
			const docRefHiddenTiles = db.collection(addressHiddenTiles).doc('cards')
			const render = []
			for (let index = 0; index < hiddenCards.length; index++) {
				const cardData = hiddenCards[index];
				if(cardData.kind !== "ghost") {
					render.push(cardData.uid)
				}

			}
			await docRefHiddenTiles.set({ render })

		} catch (error) {
			console.error("Error en el guradado de las Cards:",error)
		}
	}

	const addNewTabName = (newName) => {
		const helpArray = [...namesOfCrops];
		helpArray.push(newName)
		setNamesOfCrops([...helpArray])

	}

	const getDataUid = async (uid) => {
		const splitedUid = uid.split("@")
		if(splitedUid[1] === "node") {
			return { uid: uid, kind: "nodeLora" }
		} else if(splitedUid[1] === "nodelw" || splitedUid[1] === "lwcontroller") {
			const loraWanAddr = `${usuario.username}/loraDevices/nodes/${splitedUid[2]}/namesOfCards`;
			const loraWanDoc = db.collection(loraWanAddr).doc("names");
			const dataLoraWan = await loraWanDoc.get();
			const dataNames = dataLoraWan.data().allNames
			const kindOfLora = splitedUid[1] === "nodelw" ? "nodeLoraWan" : "loraWanController";
			return { uid: uid, kind: kindOfLora, namesOfCards: dataNames }
		} else if(splitedUid[1] === "cp") {
			const calcParameterAddr = `${usuario.username}/multiCrop/${splitedUid[0]}/calculatedParameters/${splitedUid[2]}`;
			const calcParamDoc = db.collection(calcParameterAddr).doc("nameOfCard");
			const dataCalcParam = await calcParamDoc.get();
			const dataOfParam = dataCalcParam.data()
			// console.log("Esto es dataOfParam:",dataOfParam)
			return { uid: uid, kind: "cp", nameOfCard: dataOfParam.name }
		} else {
			const address = `${usuario.username}/infoDevices/${splitedUid[0]}/${splitedUid[1]}/configModule`;
			const docRef = db.collection(address).doc(uid);
			const dataDoc = await docRef.get();
			const nameCard = dataDoc.data().item.name;
			const kindCard = dataDoc.data().item.kind;
			return { name: nameCard, kind: kindCard, uid: uid}
		}

	}

	const obtenerDatos = async () => {
		// Indicar que los datos están cargando
		setIsLoadingTiles(true);
		try {
			const documentRef = db.collection(usuario.username).doc('multiCrop');
			const arrayOfNames = [];
			const arrayOfWeeks = [];
			const arrayOfDays = [];
			const arrayOfPlaces = [];
			const arrayOfCrops = [];
			const arrayOfVariants = [];
			const arrayOfEnv = [];
			const arrayOfPhenoStage = [];
			const arrayOfIndoorSystem = [];
			const arrayOfSoilType = [];
			const arrayOfDates = [];
			const arrayOfCoordinates = [];
			const matrixOfUids = [];
			const matrixOfDataCards = [];
			const matrixOfCalcParams = [];
			const cropsInfo = await documentRef.get();
			const generalData = cropsInfo.data()
			const cropsIds = generalData.listOfCrops
			const userTimezone = generalData.timeZone;
			const lastUpdated = moment.tz(generalData.lastUpdated, userTimezone);
			const now = moment.tz(userTimezone);
			setTimeZoneSelected(userTimezone)
			setIdOfCrops(generalData.listOfCrops)
			for (let index = 0; index < generalData.totalCrops; index++) {
				const addr = `${usuario.username}/multiCrop/${cropsIds[index]}`
				const documentData = db.collection(addr).doc('data')
				const documentCalcParams = db.collection(addr).doc('calculatedParameters')
				const cropData = await documentData.get()
				const calcParamsData = await documentCalcParams.get();
				if(calcParamsData.exists) {
					const dataDoc = calcParamsData.data();
					// matrixOfCalcParams.push(dataDoc.displayOfParameters)
					matrixOfCalcParams.push(dataDoc.testDisplayOfParams)
					// console.log("Esto es testDisplayOfParams:",dataDoc.testDisplayOfParams)
				} else {
					matrixOfCalcParams.push(Array(2).fill(false))
				}
				const allStoredData = cropData.data();
				const registeredEnv = allStoredData.place === "Exterior" ? allStoredData.soilType : allStoredData.indoorSystem;
				arrayOfNames.push(allStoredData.cropName)
				arrayOfPlaces.push(allStoredData.place)
				arrayOfCrops.push(allStoredData.cultivationOf)
				arrayOfVariants.push(allStoredData.cropVariant)
				arrayOfIndoorSystem.push(allStoredData.indoorSystem)
				arrayOfSoilType.push(allStoredData.soilType)
				arrayOfEnv.push(registeredEnv)
				arrayOfPhenoStage.push(allStoredData.phenologicalStage)
				arrayOfDates.push(allStoredData.dateOfStage)
				arrayOfCoordinates.push(allStoredData.cropCoordinates)
				if(now.diff(lastUpdated, 'hours') >= 24) {
					const currentDay = Number(cropData.data().growingDay)
					const currentWeek = Number(cropData.data().growingWeek)
					let newDay = currentDay + 1;
					let newWeek = currentWeek;
          			if (newDay > 7) {
						newWeek = currentWeek + 1;
						newDay = 1
					};
					await documentData.update({ growingDay: String(newDay), growingWeek: String(newWeek)})
					await documentRef.update({ lastUpdated: now.toISOString() })
					arrayOfDays.push(String(newDay));
					arrayOfWeeks.push(String(newWeek));
				} else {
					arrayOfDays.push(cropData.data().growingDay)
					arrayOfWeeks.push(cropData.data().growingWeek)
				}
				const documentCards = db.collection(addr).doc('cards');
				const cropCards = await documentCards.get();
				matrixOfUids.push(cropCards.data().render)

			}

			for (let index = 0; index < matrixOfUids.length; index++) {
				const cardsForOneCrop = [[]]
				const data1 = [{uid:'1_0', kind:'ghost'}]
				const data2 = [{uid:'2_0', kind:'ghost'}]
				const data3 = [{uid:'3_0', kind:'ghost'}]
				const column1 = matrixOfUids[index].col_1;
				const column2 = matrixOfUids[index].col_2;
				const column3 = matrixOfUids[index].col_3;

				if(column1) {
					for (let i = 0; i < column1.length; i++) {
						const uid = column1[i];
						const dataOfUid = await getDataUid(uid)
						data1.push(dataOfUid)
					}
				}
				if(column2) {
					for (let i = 0; i < column2.length; i++) {
						const uid = column2[i];
						const dataOfUid = await getDataUid(uid)
						data2.push(dataOfUid)
					}
				}
				if(column3) {
					for (let i = 0; i < column3.length; i++) {
						const uid = column3[i];
						const dataOfUid = await getDataUid(uid)
						data3.push(dataOfUid)
					}
				}
				cardsForOneCrop.push(data1)
				cardsForOneCrop.push(data2)
				cardsForOneCrop.push(data3)

				matrixOfDataCards.push(cardsForOneCrop)
			}

			// Guardar los datos en el estado
			setAllTilesTest([...matrixOfDataCards])
			setNamesOfCrops([...arrayOfNames])
			setDaysOfCrops([...arrayOfDays])
			setWeeksOfCrops([...arrayOfWeeks])
			setPlaceOfCrops([...arrayOfPlaces])
			setCrops([...arrayOfCrops])
			setVariantsOfCrop([...arrayOfVariants])
			setPhenoStages([...arrayOfPhenoStage])
			setIndoorSystems([...arrayOfIndoorSystem])
			setSoilTypes([...arrayOfSoilType])
			setDatesOfStages([...arrayOfDates])
			setCurrentCoordinates([...arrayOfCoordinates]);
			setParametersDisplayStatus([...matrixOfCalcParams])

			// Guardar los datos de las pestañas en localStorage para acceso rápido
			if (usuario && usuario.username) {
				try {
					// Solo guardamos la estructura básica para evitar exceder el límite de localStorage
					const simplifiedTiles = matrixOfDataCards.map(crop => {
						return crop.map(column => {
							return column.map(item => ({
								uid: item.uid,
								kind: item.kind
							}));
						});
					});
					localStorage.setItem('dashboardTilesStructure', JSON.stringify(simplifiedTiles));
				} catch (error) {
					console.error('Error al guardar la estructura de tiles en localStorage:', error);
				}
			}

			// Indicar que la carga ha terminado
			setIsLoadingTiles(false);
		} catch (error) {
			console.log("Error en la obtencion de pestañas de multicultivo:",error)
			// Incluso en caso de error, indicamos que la carga ha terminado
			setIsLoadingTiles(false);
		}
	}

	const getHiddenCardsData = async () => {
		try {
			const addr = `${usuario.username}/multiCrop/hiddenCards`
			// const docRef2 = await db.collection(usuario.username).doc('renderMain').get()
			// const data = docRef2.data()
			// const render = [...data.render.col_0,...data.render.col_1,...data.render.col_2,...data.render.col_3]
			// await db.collection(addr).doc('cards').set({render})

			const documentRef = db.collection(addr).doc('cards');
			const docData = await documentRef.get();
			const uids = docData.data()
			const uidsRender = uids.render
			const dataOfUids = []
			for (let index = 0; index < uidsRender.length; index++) {
				const oneUidData = await getDataUid(uidsRender[index])
				dataOfUids.push(oneUidData)
			}
			setHiddenCards([...dataOfUids])
			setHorizontalMenuFlag(true);
		} catch (error) {
			console.error("Error en obtener las cards ocultas:",error)
		}
	}

	const moveTileToMain = useCallback((tileId) => {
		const newTiles = [...allTilesTest]; // Crea una copia del estado actual
		const newHiddenTiles = [...hiddenCards]
		let tileFound = null;
		let sourceIndex = null;
		const currentTabCards = newTiles[tabValue]
		for (let i = 1; i < currentTabCards.length; i++) {
			tileFound = currentTabCards[i].find(tile => tile.uid === tileId);
			if (tileFound) {
				sourceIndex = i; // Guardar el índice del sub-arreglo donde se encontró
				break; // Salir del ciclo una vez encontrado el tile
			}
		}
		if (tileFound && sourceIndex !== null) {
			// Eliminar el tile del sub-arreglo original
			currentTabCards[sourceIndex] = currentTabCards[sourceIndex].filter(tile => tile.uid !== tileId);
			newTiles[tabValue] = [...currentTabCards]
			setAllTilesTest([...newTiles])
			// Añadir el tile al sub-arreglo en la posición 0
			newHiddenTiles.splice(0,0,tileFound)
			setHiddenCards([...newHiddenTiles])
		}

	}, [setAllTilesTest, allTilesTest,hiddenCards,tabValue]);

	// Efecto para guardar el valor de la pestaña en localStorage cuando cambie
	useEffect(() => {
		// Solo guardar si hay un usuario válido
		if (usuario && usuario.username) {
			// console.log("tabValue cambiado a:", tabValue);
			localStorage.setItem('dashboardTabValue', tabValue.toString());
		}
	}, [tabValue, usuario]);

	// Efecto para validar que el valor de la pestaña esté dentro del rango de pestañas disponibles
	useEffect(() => {
		// Solo validar si hay un usuario válido y hay nombres de cultivos
		if (usuario && usuario.username && namesOfCrops.length > 0) {
			// Si el valor de la pestaña es mayor o igual al número de cultivos
			// (lo que significa que la pestaña no existe), establecer la pestaña a 0
			if (tabValue >= namesOfCrops.length) {
				// console.log(`Ajustando tabValue de ${tabValue} a 0 porque excede el número de cultivos (${namesOfCrops.length})`);
				setTabValue(0);
			} else {
				// console.log(`tabValue actual: ${tabValue}, número de cultivos: ${namesOfCrops.length}`);
			}
		}
	}, [namesOfCrops, tabValue, usuario]);

	useEffect(() => {
	  if(usuario && usuario.username) {
		// console.log(`Cargando datos de multicultivo para el usuario: ${usuario.username}`);
		// Cargar datos sin modificar el valor de la pestaña
		getHiddenCardsData();
		obtenerDatos();
	  } else {
		// Limpiar datos cuando no hay usuario
		console.log("Limpiando datos de multicultivo porque no hay usuario");
		// Eliminar el valor de la pestaña guardado en localStorage
		localStorage.removeItem('dashboardTabValue');
		setNamesOfCrops([]);
		setActualNumberOfCrops(1);
		//setTabValue(0);
		setAllTilesTest([[]]);
		setHiddenCards([]);
		setWeeksOfCrops([]);
		setDaysOfCrops([]);
		setTimeZoneSelected('');
		setIdOfCrops([]);
		setOpenConfigNewCrop(false);
		setOpenEditWindow(false);
		setPlaceOfCrops([]);
		setCrops([]);
		setVariantsOfCrop([]);
		setPhenoStages([]);
		setIndoorSystems([]);
		setSoilTypes([]);
		setDatesOfStages([]);
		setCurrentCoordinates([]);
		setHorizontalMenuFlag(false);
		setParametersDisplayStatus([]);
	  }
	}, [usuario])

	useEffect(() => {
		// Verificar que tenemos un usuario válido
		if (!usuario || !usuario.username) {
			console.log("No hay usuario válido para suscribirse a cambios de multicultivo");
			return;
		}

		try{
			const refreshDashboard = async (data) => {
				// Cargar datos sin modificar el valor de la pestaña
				await obtenerDatos();
				setOpenConfigNewCrop(false);
			}

			const refreshHiddenCards = async() => {
				// No establecemos tabValue a 0 aquí para mantener la pestaña seleccionada
				// El efecto que valida el rango de pestañas se encargará de ajustar el valor si es necesario
				await getHiddenCardsData();
			}

			const addr = usuario.username+'/multiCrop';
			// console.log(`Creando suscripción para cambios en multicultivo: ${addr}`);
			const unsubscribe = db.doc(addr).onSnapshot(snapshot => {
			  const data = snapshot.data();
			  if(data){

				if(data.totalCrops !== actualNumberOfCrops){
					if(Number(data.totalCrops) > actualNumberOfCrops) {
						refreshDashboard(data);

					} else {
						refreshHiddenCards();
					}
					setActualNumberOfCrops(Number(data.totalCrops));

				}
			  }

			})
			// Limpiar el listener al desmontar el componente
			return () => {
				// console.log(`Cancelando suscripción para cambios en multicultivo: ${addr}`);
				unsubscribe();
			};


		} catch(error) {
		  console.error("Error al detectar la respuesta del embebido:",error)
		}
	  }, [usuario, actualNumberOfCrops])


  return (
	<MultiCropContext.Provider
	value={{namesOfCrops, tabValue, allTilesTest, actualNumberOfCrops, hiddenCards,weeksOfCrops,daysOfCrops,
		timeZoneSelected,idOfCrops,openConfigNewCrop,openEditWindow, placeOfCrops,crops,currentCoordinates,
		horizontalMenuFlag,variantsOfCrop,phenoStages,datesOfStages,indoorSystems,soilTypes,parametersDisplayStatus,
		isLoadingTiles, // Incluimos el estado de carga
		setOpenEditWindow, setOpenConfigNewCrop,setCrops,setPlaceOfCrops,
		setTimeZoneSelected,addNewTabName, sendToCRcrop, setTabValue, setAllTilesTest,
		saveNewTilesPosition, setHiddenCards, moveTileToMain,saveTimeZone,setWeeksOfCrops,setDaysOfCrops,
		setNamesOfCrops,setCurrentCoordinates,setVariantsOfCrop,setPhenoStages,setDatesOfStages,setIndoorSystems,setSoilTypes}}>
		{ children }
	</MultiCropContext.Provider>
  )
}
