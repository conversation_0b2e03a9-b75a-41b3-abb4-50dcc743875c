import React, { useContext, useState, useEffect, useCallback } from 'react';
import { UserContext } from '../../../context/UserProvider';
import {
  Divider,
  Grid,
  Button,
  makeStyles,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
} from '@material-ui/core';
import AddIcon from '@material-ui/icons/Add';
import { withRouter } from 'react-router-dom/cjs/react-router-dom.min';
import { ConfirmationAlerts } from '../IrrigationForm/components/ConfirmationAlerts';
import WaterTankCollapsibleTable from './WaterTankCollapsibleTable';
import AddWaterTankDialog from './AddWaterTankDialog';
import { db } from '../../../config/firebase';
// moment-timezone is used in WaterTankCollapsibleTable for formatting dates


const useStyles = makeStyles((theme) => ({
  addButton: {
    margin: theme.spacing(2),
  },
  headerContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
  },
  title: {
    flexGrow: 1,
  },
}));

const WaterTankForm = () => {
  const classes = useStyles();
  const { currentMac, canIdIrrigation, usuario, togglesNames, togglesUid,
    switchesUid, levelSensorsUid, levelSensorsNames } = useContext(UserContext);

  // Estados para alertas y datos
  const [open, setOpen] = useState(false);
  const [openNotification, setOpenNotification] = useState(false)
  const [typeAlert, setTypeAlert] = useState("info");
  const [typeNotification, setTypeNotification] = useState("info")
  const [alertMessage, setAlertMessage] = useState("");
  const [notificationMessage, setNotificationMessage] = useState("");
  const [alertTitle, setAlertTitle] = useState("");
  const [notificationTitle, setNotificationTitle] = useState("");
  const [waterTanks, setWaterTanks] = useState([]);
  const [showTable, setShowTable] = useState(false);

  // Estados para el diálogo de agregar/editar tanque
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [editingTank, setEditingTank] = useState(null);

  // Estados para el diálogo de confirmación de eliminación
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [tankToDelete, setTankToDelete] = useState(null);

  const handleAlert = useCallback((openValue, type, title = "", message = "") => {
    setOpen(openValue);
    setTypeAlert(type);

    if (type === "success") {
      setAlertTitle(title || "¡Éxito!");
      setAlertMessage(message || "La operación se ha realizado con éxito");
    } else if (type === "error") {
      setAlertTitle(title || "Error");
      setAlertMessage(message || "Ha ocurrido un error al realizar la operación");
    } else if (type === "warning") {
      setAlertTitle(title || "Advertencia");
      setAlertMessage(message || "Tenga precaución al realizar esta operación");
    } else if (type === "info") {
      setAlertTitle(title || "Información");
      setAlertMessage(message || "Operación en proceso");
    }
  }, [setOpen, setTypeAlert, setAlertTitle, setAlertMessage]);

  const handleNotification = useCallback((openValue, type, title = "", message = "") => {
    setOpenNotification(openValue);
    setTypeNotification(type);

    if (type === "success") {
      setNotificationTitle(title || "¡Éxito!");
      setNotificationMessage(message || "La operación se ha realizado con éxito");
    } else if (type === "error") {
      setNotificationTitle(title || "Error");
      setNotificationMessage(message || "Ha ocurrido un error al realizar la operación");
    } else if (type === "warning") {
      setNotificationTitle(title || "Advertencia");
      setNotificationMessage(message || "Tenga precaución al realizar esta operación");
    } else if (type === "info") {
      setNotificationTitle(title || "Información");
      setNotificationMessage(message || "Operación en proceso");
    }
  }, [setOpenNotification, setTypeNotification, setNotificationTitle, setNotificationMessage]);

  // Cargar datos de los tanques de agua
  useEffect(() => {
    const fetchWaterTanks = async () => {
      if (usuario && usuario.username && currentMac && canIdIrrigation) {
        try {
          // Aquí se obtendría la información de los tanques desde Firebase
          // console.log("Esto es togglesNames:", togglesNames)
          // console.log("Esto es togglesUid:", togglesUid)
          const waterTankAddr = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/waterTankControl`;
          const waterDOcRef = db.collection(waterTankAddr).doc("tankRows");
          const snapshot = await waterDOcRef.get();
          let mockTanks = [];
          if(snapshot.exists) {
            const data = snapshot.data();
            const rowData = data.allRows;

            const tanksData = rowData.map((row, index) => {
              const fillData = row.fill
              const emptyData = row.empty
              const indexFPump = togglesUid.findIndex(uid => uid === fillData.pump)
              const indexEPump = togglesUid.findIndex(uid => uid === emptyData.pump)
              const fillingPump = togglesNames[indexFPump]
              const emptyingPump = togglesNames[indexEPump]
              return {
              id: index,
              name: row.name,
              action: row.lastAction,
              lastExecution: row.lastExecution,
              waterLevelSensor: row.levelSensor,
              recirculationValve: row.recirculationValve,
              recirculationPump: row.recirculationPump,
              details: {
                "Ultima acción": row.lastAction === "fill" ? "Llenado" : "Vaciado",
                "Bomba de llenado": fillingPump,
                "Bomba de vaciado": emptyingPump,
              },
              fillData: fillData,
              emptyData: emptyData
              }
            });
            mockTanks = tanksData;
          }

          setWaterTanks(mockTanks);
          setShowTable(true);
        } catch (error) {
          console.error("Error al cargar los tanques de agua:", error);
          handleAlert(true, "error", "Error", "No se pudieron cargar los datos de los tanques");
        }
      }
    };

    fetchWaterTanks();
  }, [usuario, currentMac, canIdIrrigation, handleAlert,togglesNames,togglesUid]);

  // Función para obtener el nivel actual de agua de un tanque
  const getWaterLevelData = async (sensorUid) => {
    if (!sensorUid) {
      console.log("No hay sensor de nivel configurado para este tanque");
      return null;
    }

    try {
      // Extraer el nodeId y sensorId del UID del sensor (formato: nodeId@sensorId)
      const sensorParts = sensorUid.split('@');
      if (sensorParts.length < 4) {
        console.error("Formato de UID de sensor inválido:", sensorUid);
        return null;
      }

      const nodeId = sensorParts[2];
      const sensorId = sensorParts[4];

      // Obtener los datos del sensor
      const docPath = `${usuario.username}/loraDevices/nodes/${nodeId}/sensors/dataSensors`;
      const docRef = await db.doc(docPath).get();

      if (!docRef.exists) {
        console.error("No se encontraron datos para el sensor:", sensorUid);
        return null;
      }

      const sensorData = docRef.data();
      if (!sensorData || !sensorData.data) {
        console.error("Estructura de datos inválida para el sensor:", sensorUid);
        return null;
      }

      // Buscar el sensor específico en el array de datos
      const sensorInfo = sensorData.data.find(sensor => sensor.id === Number(sensorId));
      if (!sensorInfo) {
        console.error("No se encontró información para el sensor ID:", sensorId);
        return null;
      }

      // Determinar el valor a usar según el tipo de medición
      let waterLevelPercentage;
      let waterLevelLiters;
      if(sensorInfo.stateSensor === "available") {
        waterLevelPercentage = sensorInfo.percentageWater;
        waterLevelLiters = sensorInfo.litersWater;
      } else {
        waterLevelPercentage = -1;
        waterLevelLiters = -1;
      }

      return { percentage: waterLevelPercentage, liters: waterLevelLiters};
    } catch (error) {
      console.error("Error al obtener datos del nivel de agua:", error);
      return null;
    }
  };

  const getContainerWaterVolume = async (containerUid) => {
    if(!containerUid) {
      console.log("No hay datso del volumen del tanque");
      return null;
    }
    try {
      // Extraer el nodeId y sensorId del UID del sensor (formato: nodeId@sensorId)
      const sensorParts = containerUid.split('@');
      if (sensorParts.length < 4) {
        console.error("Formato de UID de sensor inválido:", containerUid);
        return null;
      }
      const nodeId = sensorParts[2];
      const sensorId = sensorParts[4];

      // Obtener los datos del sensor
      const docPath = `${usuario.username}/loraDevices/nodes/${nodeId}/waterContainers/waterTankData`;
      const docRef = await db.doc(docPath).get();
      if(docRef.exists) {
        const sensorData = docRef.data().data;
        const containerData = sensorData.find(container => container.id === sensorId);
        if(containerData) {
          return containerData.capacity;
        } else {
          return null;
        }
      }

    } catch (error) {
      console.error("Error en obtener los datos del volumen del tanque:",error)
    }
  }

  // Función para obtener el estado actual de un switch específico
  const getSwitchState = async (switchUid) => {
    if (!switchUid || !usuario || !usuario.username) {
      console.log("No hay switch configurado o usuario no disponible");
      return null;
    }

    try {
      // Extraer el nodeId, canId y outId del UID del switch (formato: macId@canId@kind@outId)
      const switchParts = switchUid.split('@');
      if (switchParts.length < 4) {
        console.error("Formato de UID de switch inválido:", switchUid);
        return null;
      }

      const macId = switchParts[0];
      const canId = switchParts[1];
      const outId = switchParts[3];

      // Obtener los datos del switch desde Firebase
      const docPath = `${usuario.username}/infoDevices/${macId}/${canId}/fromModule/render`;
      const docRef = await db.doc(docPath).get();

      if (!docRef.exists) {
        console.error("No se encontraron datos para el switch:", switchUid);
        return null;
      }

      const switchData = docRef.data().C_bool;
      const resp = switchData[Number(outId)];

      return resp;
    } catch (error) {
      console.error("Error al obtener el estado del switch:", error);
      return null;
    }
  };

  // Función para calcular el tiempo estimado basado en el nivel de agua
  const calculateEstimatedTime = (waterLevel, totalMinutes, totalSeconds, action) => {
    if (!waterLevel) return null;

    // Convertir el tiempo total a segundos
    const totalTimeInSeconds = (parseInt(totalMinutes) || 0) * 60 + (parseInt(totalSeconds) || 0);
    if (totalTimeInSeconds <= 0) return null;

    // Calcular el tiempo estimado basado en el porcentaje de agua
    const percentage = waterLevel;
    let remainingPercentage;

    if (action === "fill") {
      // Si estamos llenando, el tiempo restante es proporcional al porcentaje que falta llenar
      remainingPercentage = 100 - percentage;
    } else {
      // Si estamos vaciando, el tiempo restante es proporcional al porcentaje actual
      remainingPercentage = percentage;
    }

    // Calcular el tiempo estimado en segundos
    const estimatedTimeInSeconds = (totalTimeInSeconds * remainingPercentage) / 100;

    // Convertir a minutos y segundos
    const estimatedMinutes = Math.floor(estimatedTimeInSeconds / 60);
    const estimatedSeconds = Math.floor(estimatedTimeInSeconds % 60);

    return {
      minutes: estimatedMinutes,
      seconds: estimatedSeconds,
      totalSeconds: estimatedTimeInSeconds
    };
  };

  // Función para enviar la rutina de los tanques por MQTT
  const sendingTankRoutine = async (e, n) => {
    const item = {
        msMqtt: e,
        mac: currentMac,
        action: "Tank Routine",
        fecha: Date.now(),
        uid: n
    }

    try {
        const addr = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/sendConfigModule`
        await db.collection(addr).doc("sendConfig").set({ item })
        //console.log("Esto es macId:", macId)
    } catch (error) {
        console.log(error)
    }
  }

  // Función para actualizar la última ejecución en la base de datos
  const updateTankLastExecutionInDB = async (tankId, lastExecutionTime,tankAction) => {
    try {
      console.log("Entre a waterTankLastExecutionInDB");
      const waterTankAddr = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/waterTankControl`;
      const waterDocRef = db.collection(waterTankAddr).doc("tankRows");

      // Obtener los tanques actuales
      const snapshot = await waterDocRef.get();
      if (snapshot.exists) {
        const data = snapshot.data();
        let allRows = [...data.allRows];

        // Actualizar el tanque específico
        allRows = allRows.map((row, index) => {
          if (index === tankId) {
            return { ...row, lastExecution: lastExecutionTime, lastAction: tankAction };
          }
          return row;
        });

        // Guardar en Firebase
        await waterDocRef.set({ allRows });
        console.log(`Última ejecución actualizada en DB para tanque ${tankId}:`, lastExecutionTime);
      }
    } catch (error) {
      console.error("Error al actualizar la última ejecución en la base de datos:", error);
    }
  };

  // Manejar la ejecución de una acción en un tanque
  const handleExecuteTank = async (tank, completed = false, stoppedBySwitch = false, completionTime = null) => {
    // Verificamos si el tanque ya está en ejecución (esto lo determinará el componente WaterTankCollapsibleTable)
    // Si el componente WaterTankCollapsibleTable cambia executingTankId a null, significa que estamos deteniendo
    // Si lo cambia al ID del tanque, significa que estamos ejecutando
    const moduleId = currentMac + "@" + canIdIrrigation;

    // Verificamos si hay un tanque en ejecución consultando el estado del botón en la tabla
    const isExecuting = document.querySelector(`button[data-tank-id="${tank.id}"]`)?.textContent === 'Detener';

    // Verificar si la operación se completó automáticamente por el switch
    if (completed && stoppedBySwitch) {
      // Si la operación se completó automáticamente por un cambio en el Switch
      const actionText = tank.action === "fill" ? "llenado" : "vaciado";
      const switchType = tank.action === "fill" ? "máximo" : "mínimo";
      handleAlert(true, "success", "Operación completada", `La operación de ${actionText} en ${tank.name} ha sido completada automáticamente porque se activó el sensor de nivel ${switchType}.`);

      // Actualizar la fecha y hora de finalización en la base de datos
      // Usar el completionTime pasado como parámetro o crear uno nuevo
      const finalCompletionTime = completionTime || new Date().toISOString();
      console.log("Entre a stoppedBySwitch, completionTime:", finalCompletionTime);

      await updateTankLastExecutionInDB(tank.id, finalCompletionTime,tank.action);

      // Actualizar también el estado local
      const updatedTanks = waterTanks.map(t => {
        if (t.id === tank.id) {
          return { ...t, lastExecution: finalCompletionTime };
        }
        return t;
      });
      setWaterTanks(updatedTanks);

      // Enviar comando MQTT para detener la operación
      let mqtt = "";
      const len = 15;
      const action = 244;
      const typeAction = 2;
      const fillData = tank.fillData;
      const emptyData = tank.emptyData;
      const estimatedMinutes = 0;
      const estimatedSeconds = 0;
      const totalMinutes = tank.action === "fill" ? fillData.minutes : emptyData.minutes;
      const totalSeconds = tank.action === "fill" ? fillData.seconds : emptyData.seconds;
      const minutesWaiting = tank.action === "fill" ? 0 : emptyData.minutesWaiting;

      const recirculationValve = togglesUid.findIndex(uid => uid === tank.recirculationValve);
      const fillingPump = togglesUid.findIndex(uid => uid === fillData.pump);
      const recirculationPump = togglesUid.findIndex(uid => uid === tank.recirculationPump);
      const fillingValve = togglesUid.findIndex(uid => uid === fillData.fillValve);
      const fillingWaterSourceIndex = togglesUid.findIndex(uid => uid === fillData.waterSource);
      const emptyValve = togglesUid.findIndex(uid => uid === emptyData.emptyValve);
      const emptyPumpIndex = togglesUid.findIndex(uid => uid === emptyData.pump);
      const switchIndex = switchesUid.findIndex(uid => uid === (tank.action === "fill" ? fillData.maxSwitch : emptyData.minSwitch));

      mqtt =
          len +
          "," +
          action +
          "," +
          canIdIrrigation +
          "," +
          typeAction +
          "," +
          switchIndex +
          "," +
          estimatedMinutes +
          "," +
          estimatedSeconds +
          "," +
          totalMinutes +
          "," +
          totalSeconds +
          "," +
          minutesWaiting +
          "," +
          fillingPump +
          "," +
          emptyPumpIndex +
          "," +
          recirculationPump +
          "," +
          fillingWaterSourceIndex +
          "," +
          recirculationValve +
          "," +
          fillingValve +
          "," +
          emptyValve;

      // console.log("Esto es mqtt para detener operación completada:", mqtt);
      sendingTankRoutine(mqtt, moduleId);

      return; // Salir de la función después de manejar la finalización automática
    }

    if (isExecuting && !completed) {//Se detiene la accion de llenado/vaciado
      // Si estamos deteniendo la acción y no ha terminado la operación
      // Verificamos si la detención fue por acción del usuario
      if (!stoppedBySwitch) {
        // Si la detención fue por acción del usuario
        handleAlert(true, "warning", "Operación detenida", `Se ha detenido la acción ${tank.action} en ${tank.name} por acción del usuario.`);
      }

      let mqtt = "";
      const len = 15;
      const action = 244;
      const typeAction = 2;
      const fillData = tank.fillData;
      const emptyData = tank.emptyData;
      const estimatedMinutes = 0;
      const estimatedSeconds = 0;
      const totalMinutes = tank.action === "fill" ? fillData.minutes : emptyData.minutes;
      const totalSeconds = tank.action === "fill" ? fillData.seconds : emptyData.seconds;
      const minutesWaiting = tank.action === "fill" ? 0 : emptyData.minutesWaiting;

      const recirculationValve = togglesUid.findIndex(uid => uid === tank.recirculationValve);
      const fillingPump = togglesUid.findIndex(uid => uid === fillData.pump);
      const recirculationPump = togglesUid.findIndex(uid => uid === tank.recirculationPump);
      const fillingValve = togglesUid.findIndex(uid => uid === fillData.fillValve);
      const fillingWaterSourceIndex = togglesUid.findIndex(uid => uid === fillData.waterSource);
      const emptyValve = togglesUid.findIndex(uid => uid === emptyData.emptyValve);
      const emptyPumpIndex = togglesUid.findIndex(uid => uid === emptyData.pump);
      const switchIndex = switchesUid.findIndex(uid => uid === (tank.action === "fill" ? fillData.maxSwitch : emptyData.minSwitch));

      

      mqtt =
          len +
          "," +
          action +
          "," +
          canIdIrrigation +
          "," +
          typeAction +
          "," +
          switchIndex +
          "," +
          estimatedMinutes +
          "," +
          estimatedSeconds +
          "," +
          totalMinutes +
          "," +
          totalSeconds +
          "," +
          minutesWaiting +
          "," +
          fillingPump +
          "," +
          emptyPumpIndex +
          "," +
          recirculationPump +
          "," +
          fillingWaterSourceIndex +
          "," +
          recirculationValve +
          "," +
          fillingValve +
          "," +
          emptyValve;
            
        // console.log("Esto es mqtt:", mqtt);
        sendingTankRoutine(mqtt, moduleId);

    } else if (!completed) {//Se ejecuta la ccion por primera vez
      // Si estamos iniciando la acción y no ha terminado la operación, primero verificamos el estado de los switches de seguridad
      const fillData = tank.fillData;
      const emptyData = tank.emptyData;
      console.log("Esto es tank:", tank);

      // Obtener los UIDs de los switches de nivel máximo y mínimo
      const maxLevelSwitchUid = fillData.maxSwitch;
      const minLevelSwitchUid = emptyData.minSwitch;

      // Verificar el estado de los switches según la acción a realizar
      if (tank.action === "fill") {
        // Para llenado, verificar que el switch de nivel máximo no esté activado (1)
        if (maxLevelSwitchUid) {
          const maxSwitchState = await getSwitchState(maxLevelSwitchUid);

          if (maxSwitchState === "1") {
            // Si el switch de nivel máximo está activado, mostrar alerta y no ejecutar la acción
            handleAlert(
              true,
              "warning",
              "Operación no permitida",
              `No se puede ejecutar el llenado en ${tank.name} porque el sensor de nivel máximo está activado.`
            );
            return; // Salir de la función sin ejecutar la acción
          }
        }
        const waterSourceUid = fillData.waterSourceLevelSensor;
        const waterSourceIndex = levelSensorsUid.findIndex(uid => uid === waterSourceUid);
        const waterSourceName = waterSourceUid !== null ? levelSensorsNames[waterSourceIndex] : null;

        if(waterSourceUid !== null && tank.waterLevelSensor !== null) {
          try {
            const waterLevelSource = await getWaterLevelData(waterSourceUid);
            const waterCapacityFillTank = await getContainerWaterVolume(tank.waterLevelSensor);
            if(waterLevelSource && waterLevelSource.liters !== -1) {
              if(waterCapacityFillTank && waterCapacityFillTank !== 0) {
                if (waterLevelSource.liters < waterCapacityFillTank) {
                  handleAlert(
                    true,
                    "warning",
                    "Operación no permitida",
                    `No se puede ejecutar el llenado en ${tank.name} porque el tanque de origen "${waterSourceName}" no tiene suficiente agua.`
                  );
                  return; // Salir de la función sin ejecutar la acción
                }
              } else {
                handleAlert(
                  true,
                  "warning",
                  "Operación no permitida",
                  `No se puede ejecutar el llenado en ${tank.name} porque se desconoce su volumen máximo.Completa la informacion del tanque en la configuración de la card.`
                );
                return; // Salir de la función sin ejecutar la acción
              }

            } else {
              handleAlert(
                true,
                "warning",
                "Operación no permitida",
                `No se puede ejecutar el llenado en ${tank.name} porque se desconoce el nivel del tanque de origen "${waterSourceName}".Revisar sensor y/o configuracion de la card.`
              );
              return; // Salir de la función sin ejecutar la acción
            }
            
          } catch (error) {
            console.error("Error en la verificacion del nivel del tanque de origen:", error);
          }
        }
      } else if (tank.action === "empty") {
        // Para vaciado, verificar que el switch de nivel mínimo no esté desactivado (0)
        if (minLevelSwitchUid) {
          const minSwitchState = await getSwitchState(minLevelSwitchUid);

          if (minSwitchState === "0") {
            // Si el switch de nivel mínimo está desactivado, mostrar alerta y no ejecutar la acción
            handleAlert(
              true,
              "warning",
              "Operación no permitida",
              `No se puede ejecutar el vaciado en ${tank.name} porque el sensor de nivel mínimo está desactivado.`
            );
            return; // Salir de la función sin ejecutar la acción
          }
        }

        // Verificar el nivel de agua del tanque de destino (targetTank) para la acción de vaciar
        const targetTankUid = emptyData.targetTankWaterLevelS;
        const targetTankIndex = levelSensorsUid.findIndex(uid => uid === targetTankUid);
        const targetTankName = targetTankUid !== null ? levelSensorsNames[targetTankIndex] : null;
        if (targetTankUid !== null) {
          try {
            // Obtener el nivel actual de agua del tanque de destino
            const targetWaterLevel = await getWaterLevelData(targetTankUid);
            const targetWaterCapacity = await getContainerWaterVolume(targetTankUid);
            const maxCapacityOfTank = await getContainerWaterVolume(tank.waterLevelSensor);
            
            const TotalEstimatedWaterVolume = targetWaterLevel.liters + maxCapacityOfTank;

            if (targetWaterLevel && targetWaterLevel.liters !== -1 ) {
              if(targetWaterCapacity && targetWaterCapacity !== 0) {
                if(maxCapacityOfTank && maxCapacityOfTank !== 0){
                  if(TotalEstimatedWaterVolume > targetWaterCapacity) { //Si el nivel de agua supera a la capcidad del tanque de destino, no se puede ejecutar la acción de vaciado
                    const minimumCapacityToReceiveWater = Number(targetWaterCapacity) - Number(maxCapacityOfTank);
                    const minimumPercentage = (minimumCapacityToReceiveWater * 100) / Number(targetWaterCapacity);
                    handleAlert(
                      true,
                      "warning",
                      "Operación no permitida",
                      `No se puede ejecutar el vaciado en ${tank.name} porque el tanque de destino "${targetTankName}" está demasiado lleno (${targetWaterLevel.percentage}%). El nivel debe ser menor o igual al ${minimumPercentage.toFixed(0)}%.`
                    );
                    return; // Salir de la función sin ejecutar la acción
                  }

                } else {
                  handleAlert(
                    true,
                    "warning",
                    "Operación no permitida",
                    `No se puede ejecutar el vaciado en ${tank.name} porque se desconoce su volumen máximo. Es necesario configurar los datos en el sensor de nivel en el tanque que se quiere vaciar.`
                  );
                  return; // Salir de la función sin ejecutar la acción
                }
              } else  {
                handleAlert(
                    true,
                    "warning",
                    "Operación no permitida",
                    `No se puede ejecutar el vaciado en ${tank.name} porque se desconoce el volumen máximo del tanque de destino "${targetTankName}". Es necesario configurar los datos del sensor de nivel en el tanque de destino.`
                  );
                  return; // Salir de la función sin ejecutar la acción
              }
              
            } else if(targetWaterLevel && targetWaterLevel === -1) {
              handleAlert(
                true,
                "warning",
                "Operación no permitida",
                `No se puede ejecutar el vaciado en ${tank.name} porque se desconoce el nivel del tanque de destino "${targetTankName}". Es necesario configurar los datos del sensor de nivel en el tanque de destino.`
              );
              return; // Salir de la función sin ejecutar la acción
            }
          } catch (error) {
            console.error("Error al verificar el nivel del tanque de destino:", error);
            // En caso de error, continuamos con la operación pero registramos el error
          }

        }
      }

      let estimatedMinutes = 0;
      let estimatedSeconds = 0;
      // Si estamos iniciando la acción, primero consultamos el nivel de agua actual
      if (tank.waterLevelSensor) {
        try {
          // Obtener el nivel actual de agua
          const waterLevel = await getWaterLevelData(tank.waterLevelSensor);
          const waterLevelSensorIndex = levelSensorsUid.findIndex(uid => uid === tank.waterLevelSensor);
          const waterLevelSensorName = levelSensorsNames[waterLevelSensorIndex];
          if (waterLevel && waterLevel.percentage !== -1) {
            // Calcular el tiempo estimado basado en el nivel de agua
            const actionData = tank.action === "fill" ? tank.fillData : (tank.action === "empty" ? tank.emptyData : null);
            const totalMinutes = actionData !== null ? actionData.minutes : 0;
            const totalSeconds = actionData !== null ? actionData.seconds : 0;

            const estimated = (tank.action === "fill" || tank.action === "empty") ? 
            calculateEstimatedTime(waterLevel.percentage, totalMinutes, totalSeconds, tank.action)
            :
            null;
            if (estimated) {
              estimatedMinutes = estimated.minutes;
              estimatedSeconds = estimated.seconds;
            }

            // Mostrar alerta con la información del nivel y tiempo estimado
            let timeInfo = '';
            if (estimated) {
              timeInfo = `${estimated.minutes} min ${estimated.seconds} seg`;
            } else {
              timeInfo = 'No disponible';
            }

            const actionText = tank.action === "fill" ? "llenado" : (tank.action === "empty" ? "vaciado" : "recirculación");
            if(tank.action === "fill" || tank.action === "empty"){            
              handleAlert(
                true,
                "info",
                "Operación en proceso",
                `Ejecutando ${actionText} en ${tank.name}.\nNivel actual: ${waterLevel.percentage}%.\nTiempo estimado: ${timeInfo}.`
              );
              if(actionText === "vaciado") {
                handleNotification(
                  true,
                  "info",
                  "Preparacion de nuevos Nutrientes",
                  `Si el tanque ${tank.name} es de nutrientes se recomienda preparar los nuevos nutrientes para el siguiente riego.`
                )
              }
            } else if(tank.action === "recirculate") {
              handleAlert(
                true,
                "info",
                "Operación en proceso",
                `Ejecutando ${actionText} en ${tank.name}.\nNivel actual: ${waterLevel.percentage}%.`
              );
            }
          } else {
            // Si no se pudo obtener el nivel, mostrar alerta genérica
            handleAlert(true, "warning",
              "No se pudo enviar la acción",
              `La acción ${tank.action} en ${tank.name} no se pudo completar debido a que no se tiene informacion del sensor de nivel ${waterLevelSensorName}.`);
            return; // Salir de la función sin ejecutar la acción
          }
        } catch (error) {
          console.error("Error al obtener el nivel de agua:", error);
          handleAlert(true, "error", "No se pudo obtener el nivel de agua", `No se pudo obtener el nivel de agua asociado al tanque ${tank.name}`);
        }
      } else {
        // Si no hay sensor de nivel configurado, mostrar alerta genérica
        handleAlert(true, "warining", "No hay sensor de nivel configurado", `No se puede ejecutar la acción ${tank.action} en ${tank.name}`);
        return; // Salir de la función sin ejecutar la acción
      }

      // Preparar y enviar el comando MQTT
      let mqtt = "";
      const len = 15;
      const action = 244;
      const typeAction = tank.action === "fill" ? 1 : (tank.action === "empty" ? 0 : 3);
      const totalMinutes = tank.action === "fill" ? fillData.minutes : (tank.action === "empty" ? emptyData.minutes : 0);
      const totalSeconds = tank.action === "fill" ? fillData.seconds : (tank.action === "empty" ? emptyData.seconds : 0);
      const minutesWaiting = tank.action === "fill" ? 0 : (tank.action === "empty" ? emptyData.minutesWaiting : 0);

      const recirculationValve = togglesUid.findIndex(uid => uid === tank.recirculationValve);
      const fillingPump = togglesUid.findIndex(uid => uid === fillData.pump);
      const recirculationPump = togglesUid.findIndex(uid => uid === tank.recirculationPump);
      const fillingValve = togglesUid.findIndex(uid => uid === fillData.fillValve);
      const fillingWaterSourceIndex = togglesUid.findIndex(uid => uid === fillData.waterSource);
      const emptyValve = togglesUid.findIndex(uid => uid === emptyData.emptyValve);
      const emptyPumpIndex = togglesUid.findIndex(uid => uid === emptyData.pump);
      const switchIndex = switchesUid.findIndex(uid => uid === (tank.action === "fill" ? fillData.maxSwitch : emptyData.minSwitch));
            
      
      mqtt =
          len +
          "," +
          action +
          "," +
          canIdIrrigation +
          "," +
          typeAction +
          "," +
          switchIndex +
          "," +
          estimatedMinutes +
          "," +
          estimatedSeconds +
          "," +
          totalMinutes +
          "," +
          totalSeconds +
          "," +
          minutesWaiting +
          "," +
          fillingPump +
          "," +
          emptyPumpIndex +
          "," +
          recirculationPump +
          "," +
          fillingWaterSourceIndex +
          "," +
          recirculationValve +
          "," +
          fillingValve +
          "," +
          emptyValve;
          
      // console.log("Esto es mqtt:", mqtt);
      sendingTankRoutine(mqtt, moduleId);

      // Nota: El tiempo de última ejecución se actualizará cuando la operación se complete exitosamente
      // (cuando stoppedBySwitch sea true), no al inicio de la operación
    }
  };

  // Manejar la edición de un tanque
  const handleEditTank = (tank) => {
    // Si el clic viene del selector de acción, solo actualizamos la acción
    if (tank.action) {
      handleAlert(true, "info", "Acción cambiada", `Acción cambiada a ${tank.action === "fill" ? "Llenar" : "Vaciar"} para ${tank.name}`);

      setWaterTanks(prevTanks => {
        const updatedTanks = prevTanks.map(t => {
          if (t.id === tank.id) {
            return { ...t, action: tank.action };
          }
          return t;
        });
        return updatedTanks;
      });
    } else {
      // Si el clic viene del botón de editar, abrimos el diálogo de edición
      handleAlert(true, "info", "Edición", `Editando configuración de ${tank.name}`);

      // Establecer el tanque que se está editando y abrir el diálogo
      setEditingTank(tank);
      setOpenAddDialog(true);
    }
  };

  // Manejar la eliminación de un tanque
  const handleDeleteTank = (tank) => {
    setTankToDelete(tank);
    setOpenDeleteDialog(true);
  };

  // Cerrar el diálogo de confirmación de eliminación
  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    setTankToDelete(null);
  };

  // Confirmar y ejecutar la eliminación del tanque
  const handleConfirmDeleteTank = async () => {
    if (!tankToDelete) return;

    try {
      // Eliminar de Firebase
      const waterTankAddr = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/waterTankControl`;
      const waterDocRef = db.collection(waterTankAddr).doc("tankRows");

      // Obtener los tanques actuales
      const snapshot = await waterDocRef.get();
      if (snapshot.exists) {
        const data = snapshot.data();
        let allRows = [...data.allRows];

        // Eliminar el tanque del array usando el índice (id)
        allRows.splice(tankToDelete.id, 1);

        // Guardar el array actualizado en Firebase
        await waterDocRef.set({ allRows });

        // Actualizar el estado local eliminando el tanque y reindexando
        const updatedTanks = waterTanks
          .filter(tank => tank.id !== tankToDelete.id)
          .map((tank, index) => ({
            ...tank,
            id: index // Reindexar los IDs para mantener la consistencia
          }));

        setWaterTanks(updatedTanks);

        // Mostrar mensaje de éxito
        handleAlert(true, "success", "Tanque eliminado", `El tanque "${tankToDelete.name}" ha sido eliminado correctamente`);
      }
    } catch (error) {
      console.error("Error al eliminar el tanque:", error);
      handleAlert(true, "error", "Error", `No se pudo eliminar el tanque "${tankToDelete.name}": ${error.message}`);
    } finally {
      // Cerrar el diálogo
      handleCloseDeleteDialog();
    }
  };

  // Abrir el diálogo para agregar un nuevo tanque
  const handleOpenAddDialog = () => {
    setOpenAddDialog(true);
  };

  // Cerrar el diálogo para agregar/editar un tanque
  const handleCloseAddDialog = () => {
    setOpenAddDialog(false);
    setEditingTank(null); // Limpiar el tanque en edición
  };

  // Manejar el éxito al agregar o actualizar un tanque
  const handleAddSuccess = (allRows, isEditing = false) => {
    handleCloseAddDialog();

    if (isEditing) {
      handleAlert(true, "success", "Tanque actualizado", "El tanque se ha actualizado correctamente");
    } else {
      handleAlert(true, "success", "Tanque agregado", "El tanque se ha agregado correctamente");
    }

    // Transformar los datos de Firebase al formato esperado por WaterTankCollapsibleTable
    const formattedTanks = allRows.map((row, index) => {
      const fillData = row.fill;
      const emptyData = row.empty;
      const indexFPump = togglesUid.findIndex(uid => uid === fillData.pump);
      const indexEPump = togglesUid.findIndex(uid => uid === emptyData.pump);
      const fillingPump = indexFPump !== -1 ? togglesNames[indexFPump] : "No encontrado";
      const emptyingPump = indexEPump !== -1 ? togglesNames[indexEPump] : "No encontrado";

      return {
        id: index,
        name: row.name,
        action: row.lastAction,
        lastExecution: row.lastExecution,
        waterLevelSensor: row.levelSensor,
        recirculationValve: row.recirculationValve,
        details: {
          "Ultima acción": row.lastAction === "fill" ? "Llenado" : "Vaciado",
          "Bomba de llenado": fillingPump,
          "Bomba de vaciado": emptyingPump,
        },
        fillData: fillData,
        emptyData: emptyData
      };
    });

    setWaterTanks(formattedTanks);
    setShowTable(true);
  };

  return (
    <>
      <div className={classes.headerContainer}>
        <Grid
          container
          justifyContent="space-between"
          alignItems="center"
        >
          <Grid item className={classes.title}>
            <h2>Control de Tanques de Agua</h2>
          </Grid>
          <Grid item>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              className={classes.addButton}
              onClick={handleOpenAddDialog}
            >
              Agregar
            </Button>
          </Grid>
        </Grid>
      </div>
      <Divider />

      <div style={{ marginTop: '20px', marginBottom: '20px' }}>
        {showTable && (
          <WaterTankCollapsibleTable
            tanks={waterTanks}
            onExecute={handleExecuteTank}
            onEdit={handleEditTank}
            onDelete={handleDeleteTank}
            handleAlert={handleAlert}
          />
        )}
      </div>

      {/* Diálogo para agregar o editar un tanque */}
      <AddWaterTankDialog
        open={openAddDialog}
        onClose={handleCloseAddDialog}
        onSuccess={handleAddSuccess}
        editingTank={editingTank}
      />

      {/* Diálogo de confirmación para eliminar tanque */}
      <Dialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title">
          Confirmar eliminación
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-dialog-description">
            ¿Está seguro de que desea eliminar el tanque "{tankToDelete?.name}"?
            <br />
            Esta acción no se puede deshacer y se eliminará toda la configuración asociada al tanque.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog} color="primary">
            Cancelar
          </Button>
          <Button onClick={handleConfirmDeleteTank} color="secondary" autoFocus>
            Eliminar
          </Button>
        </DialogActions>
      </Dialog>

      <div>
        <ConfirmationAlerts
          open={open}
          setOpen={setOpen}
          typeAlert={typeAlert}
          message={alertMessage}
          alertTitle={alertTitle}
        />
      </div>
      <div>
        <ConfirmationAlerts
          open={openNotification}
          setOpen={setOpenNotification}
          typeAlert={typeNotification}
          message={notificationMessage}
          alertTitle={notificationTitle}
        />
      </div>
    </>
  );
};

export default withRouter(WaterTankForm);
export { WaterTankForm };
