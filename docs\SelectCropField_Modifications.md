# Modificaciones al componente SelectCropField.jsx

## Resumen de cambios

Se ha modificado el componente `SelectCropField.jsx` para implementar la funcionalidad solicitada de procesamiento de archivos KML/KMZ con selección y edición de polígonos.

## Nuevas funcionalidades implementadas

### 1. Estados agregados
- `polygonList`: Array de objetos con información de cada polígono
- `showPolygonList`: Boolean para mostrar/ocultar la lista de polígonos
- `editingIndex`: Índice del polígono que se está editando
- `tempName`: Nombre temporal durante la edición

### 2. Funciones agregadas

#### `handlePolygonToggle(index)`
Permite seleccionar/deseleccionar polígonos individuales mediante checkbox.

#### `handleEditName(index)`
Inicia la edición del nombre de un polígono específico.

#### `handleSaveName(index)`
Guarda el nombre editado del polígono.

#### `handleCancelEdit()`
Cancela la edición del nombre actual.

#### `handleProcessSelectedPolygons()`
Procesa los polígonos seleccionados y los envía a la Cloud Function.

### 3. Modificaciones en `onFileChange`
- Ahora crea un array de objetos `polygonList` en lugar de procesar directamente
- Cada objeto contiene: `name`, `geoJsonData`, `selected`
- Muestra la interfaz de selección en lugar de cerrar el diálogo inmediatamente

### 4. Interfaz de usuario mejorada

#### Lista de polígonos
- Muestra todos los polígonos encontrados en el archivo
- Checkbox para seleccionar/deseleccionar cada polígono
- Campo de texto editable para cambiar nombres
- Botones de guardar/cancelar para la edición
- Icono de edición para cada polígono

#### Diálogo expandido
- Tamaño máximo `md` y `fullWidth` para mejor visualización
- Navegación entre vista inicial y lista de polígonos
- Botón "Volver" para regresar a la selección inicial
- Botón "Procesar Seleccionados" con contador de polígonos seleccionados

## Estructura de datos

### Objeto polígono en `polygonList`
```javascript
{
  name: "Nombre del polígono",
  geoJsonData: {
    type: "Feature",
    geometry: { type: "Polygon", coordinates: [...] },
    properties: { name: "..." }
  },
  selected: true/false
}
```

### Datos enviados a Cloud Function
```javascript
{
  username: "usuario",
  polygons: [
    {
      polygonName: "Nombre editado",
      geoJsonData: { /* objeto GeoJSON completo */ },
      timestamp: "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

## Flujo de trabajo

1. **Selección de archivo**: Usuario hace clic en "Subir archivo"
2. **Carga de archivo**: Se lee el archivo KML/KMZ
3. **Extracción de polígonos**: Se convierten a GeoJSON y se almacenan en `polygonList`
4. **Mostrar lista**: Se muestra la interfaz con todos los polígonos encontrados
5. **Edición y selección**: Usuario puede:
   - Editar nombres haciendo clic en el icono de edición
   - Seleccionar/deseleccionar polígonos con checkbox
6. **Procesamiento**: Al hacer clic en "Procesar Seleccionados":
   - Se filtran solo los polígonos seleccionados
   - Se crea el array con formato requerido (nombre, geoJSON, timestamp)
   - Se envía a la Cloud Function
   - Se actualiza el estado del componente con los polígonos procesados

## Componentes Material-UI utilizados

- `List`, `ListItem`, `ListItemText`, `ListItemSecondaryAction`
- `Checkbox` para selección múltiple
- `TextField` para edición de nombres
- `IconButton` con `EditIcon` para iniciar edición
- `Dialog` expandido con `maxWidth="md"` y `fullWidth`

## Validaciones implementadas

- Verificación de que al menos un polígono esté seleccionado antes del procesamiento
- Manejo de errores en la comunicación con Cloud Function
- Reseteo de estados al cerrar el diálogo
- Validación de estructura de archivos KML/KMZ

## Próximos pasos

1. **Implementar Cloud Function**: Usar la documentación en `docs/CloudFunction_savePolygonData.md`
2. **Testing**: Probar con archivos KML/KMZ reales
3. **Manejo de errores**: Mejorar mensajes de error específicos
4. **UX**: Considerar agregar indicadores de carga durante el procesamiento

## Archivos modificados

- `src/components/Views/Maps/SelectCropField.jsx` - Componente principal modificado
- `docs/CloudFunction_savePolygonData.md` - Documentación de Cloud Function (nuevo)
- `docs/SelectCropField_Modifications.md` - Este archivo de documentación (nuevo)

## Dependencias

No se requieren nuevas dependencias. Se utilizan:
- Material-UI components ya existentes en el proyecto
- `togeojson` y `jszip` ya importados
- Firebase/Firestore ya configurado
